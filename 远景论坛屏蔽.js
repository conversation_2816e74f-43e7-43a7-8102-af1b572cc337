// ==UserScript==
// @name         远景论坛屏蔽
// @version      1.0
// @description  屏蔽特定用户的帖子和主题，支持动态加载内容，并添加关键词屏蔽功能
// <AUTHOR>
// @match        https://*.pcbeta.com/*
// @match        https://bbs.d.163.com/*
// @match        https://*.pcbeta.com/forum.php?mod=viewthread&tid=*
// @grant        GM_getValue
// @grant        GM_setValue
// ==/UserScript==

(function () {
	"use strict";

	// 从暴力猴存储中加载已屏蔽的用户列表和关键词列表
	var blockedUsers = GM_getValue("blockedUsers", []);
	var blockedKeywords = GM_getValue("blockedKeywords", []);

	// 保存屏蔽列表到暴力猴存储
	function saveBlockedLists() {
		GM_setValue("blockedUsers", blockedUsers);
		GM_setValue("blockedKeywords", blockedKeywords);
	}

	// 检查并隐藏帖子列表中的行或具体帖子
	function checkAndHide(element) {
		// 检查是否为被屏蔽的用户
		if (
			element.href &&
			blockedUsers.some((userID) =>
				element.href.includes("space-uid-" + userID)
			)
		) {
			hidePostOrRow(element);
			return;
		}

		// 检查帖子或标题中是否包含屏蔽关键词
		let container = element.closest(
			'div[id^="post_"], table[id^="pid"], tbody'
		);
		if (container) {
			let textContent = (
				container.innerText ||
				container.textContent ||
				""
			).toLowerCase();
			if (
				textContent &&
				blockedKeywords.some((keyword) =>
					textContent.toLowerCase().includes(keyword.toLowerCase())
				)
			) {
				hidePostOrRow(container);
			}
		}
	}

	// 隐藏帖子或帖子列表中的行
	function hidePostOrRow(element) {
		if (!element) return;

		// 隐藏具体帖子内容
		let postContainer = element.closest('div[id^="post_"], table[id^="pid"]');
		if (postContainer) {
			postContainer.style.display = "none";
			return;
		}

		// 隐藏帖子列表中的行
		let threadRow = element.closest("tbody");
		if (threadRow) {
			threadRow.style.display = "none";
		}
	}

	// 初始检查页面上的帖子列表和内容
	function initialCheck() {
		// 检查用户ID和关键词
		document
			.querySelectorAll(
				'a[href*="space-uid-"], div[id^="post_"], table[id^="pid"], tbody'
			)
			.forEach(checkAndHide);

		// 检查帖子列表中的标题
		document
			.querySelectorAll('a[href*="forum.php?mod=viewthread"]')
			.forEach((threadLink) => {
				let textContent = (
					threadLink.innerText ||
					threadLink.textContent ||
					""
				).toLowerCase();
				if (
					blockedKeywords.some((keyword) =>
						textContent.toLowerCase().includes(keyword.toLowerCase())
					)
				) {
					hidePostOrRow(threadLink);
				}
			});
	}

	// 动态加载内容监控
	var observer = new MutationObserver(function (mutations) {
		mutations.forEach(function (mutation) {
			if (mutation.addedNodes && mutation.addedNodes.length > 0) {
				mutation.addedNodes.forEach(function (node) {
					if (node.nodeType === Node.ELEMENT_NODE) {
						if (
							node.matches(
								'a[href*="space-uid-"], div[id^="post_"], table[id^="pid"], tbody'
							)
						) {
							checkAndHide(node);
						} else {
							node
								.querySelectorAll(
									'a[href*="space-uid-"], div[id^="post_"], table[id^="pid"], tbody'
								)
								.forEach(checkAndHide);
						}

						// 检查新增的帖子列表标题
						node
							.querySelectorAll('a[href*="forum.php?mod=viewthread"]')
							.forEach((threadLink) => {
								let textContent = (
									threadLink.innerText ||
									threadLink.textContent ||
									""
								).toLowerCase();
								if (
									blockedKeywords.some((keyword) =>
										textContent.toLowerCase().includes(keyword.toLowerCase())
									)
								) {
									hidePostOrRow(threadLink);
								}
							});
					}
				});
			}
		});
	});

	// 初始检查和观察启动
	initialCheck();
	observer.observe(document.body, {
		childList: true,
		subtree: true,
	});

	// 添加一个按钮，用于显示和编辑屏蔽列表
	function addBlockListButton() {
		// 新增判断：禁止在iframe中创建按钮
		if (window.self !== window.top) {
			return;
		}

		// 创建按钮（保持原有代码不变）
		var button = document.createElement("button");
		button.innerText = "屏蔽列表";
		button.style.position = "fixed";
		button.style.bottom = "20px";
		button.style.right = "20px";
		button.style.zIndex = 1000;
		button.style.padding = "5px 10px";
		button.style.backgroundColor = "#1C85DC";
		button.style.boxShadow = "0 0 6px rgba(0, 0, 0, 0.2)";
		button.style.color = "#fff";
		button.style.border = "none";
		button.style.borderRadius = "6px";
		button.style.cursor = "pointer";

		// 点击按钮时弹出对话框
		button.addEventListener("click", function () {
			showBlockListDialog();
		});

		// 将按钮添加到页面
		document.body.appendChild(button);
	}

	// 显示屏蔽列表的对话框
	function showBlockListDialog() {
		// 创建对话框
		var dialog = document.createElement("div");
		dialog.style.position = "fixed";
		dialog.style.top = "50%";
		dialog.style.left = "50%";
		dialog.style.transform = "translate(-50%, -50%)";
		dialog.style.backdropFilter = "blur(50px)"; // 调整模糊程度
		dialog.style.backgroundColor = "rgba(255, 255, 255, 0.7)"; // 添加半透明背景
		dialog.style.padding = "20px";
		dialog.style.borderRadius = "10px";
		dialog.style.boxShadow = "0 1px 20px rgba(0, 0, 0, 0.2)";
		dialog.style.zIndex = 1001;

		// 显式定义弹出框的大小
		dialog.style.width = "500px"; // 设置宽度
		dialog.style.height = "auto"; // 设置高度
		dialog.style.overflow = "auto"; // 如果内容超出，显示滚动条

		// 创建关闭按钮
		var closeButton = document.createElement("button");
		closeButton.innerText = "关闭";
		closeButton.style.position = "absolute";
		closeButton.style.top = "10px";
		closeButton.style.right = "10px";
		closeButton.style.backgroundColor = "transparent";
		closeButton.style.border = "none";
		closeButton.style.cursor = "pointer";

		// 关闭对话框
		closeButton.addEventListener("click", function () {
			document.body.removeChild(dialog);
		});

		// 显示当前屏蔽的用户ID
		var userList = document.createElement("div");
		userList.innerHTML = "<h3>屏蔽的用户ID:</h3>" + blockedUsers.join(", ");

		// 显示当前屏蔽的关键词
		var keywordList = document.createElement("div");
		keywordList.innerHTML =
			"<h3>屏蔽的关键词:</h3>" + blockedKeywords.join(", ");

		// 创建添加用户ID的输入框和按钮
		var addUserInput = document.createElement("input");
		addUserInput.type = "text";
		addUserInput.placeholder = "输入用户ID";
		addUserInput.style.border = "1px solid #dddddd";
		addUserInput.style.backgroundColor = "#fff";
		addUserInput.style.borderRadius = "8px";
		addUserInput.style.fontSize = "12px";
		addUserInput.style.padding = "5px 10px";

		var addUserButton = document.createElement("button");
		addUserButton.innerText = "添加用户ID";
		addUserButton.style.marginLeft = "10px";
		addUserButton.style.borderRadius = "8px";
		addUserButton.style.border = "0px solid black";
		addUserButton.style.backgroundColor = "#1C85DC";
		addUserButton.style.padding = "5px 10px";
		addUserButton.style.margin = "5px 15px";
		addUserButton.style.color = "white";
		addUserButton.style.fontSize = "12px";

		// 添加用户ID
		addUserButton.addEventListener("click", function () {
			var newUserID = parseInt(addUserInput.value.trim());
			if (!isNaN(newUserID) && !blockedUsers.includes(newUserID)) {
				blockedUsers.push(newUserID);
				userList.innerHTML = "<h3>屏蔽的用户ID:</h3>" + blockedUsers.join(", ");
				addUserInput.value = ""; // 清空输入框
				saveBlockedLists(); // 保存到 localStorage
				initialCheck(); // 重新检查页面内容
			}
		});

		// 创建添加关键词的输入框和按钮
		var addKeywordInput = document.createElement("input");
		addKeywordInput.type = "text";
		addKeywordInput.placeholder = "输入关键词";
		addKeywordInput.style.borderRadius = "8px";
		addKeywordInput.style.border = "1px solid #dddddd";
		addKeywordInput.style.fontSize = "12px";
		addKeywordInput.style.padding = "5px 10px";

		var addKeywordButton = document.createElement("button");
		addKeywordButton.innerText = "添加关键词";
		addKeywordButton.style.marginLeft = "10px";
		addKeywordButton.style.borderRadius = "8px";
		addKeywordButton.style.border = "0px solid black";
		addKeywordButton.style.backgroundColor = "#1C85DC";
		addKeywordButton.style.padding = "5px 10px";
		addKeywordButton.style.margin = "5px 15px";
		addKeywordButton.style.color = "white";
		addKeywordButton.style.fontSize = "12px";

		// 添加关键词
		addKeywordButton.addEventListener("click", function () {
			var newKeyword = addKeywordInput.value.trim();
			// 检查是否已存在（不区分大小写）
			if (
				newKeyword &&
				!blockedKeywords.some(
					(keyword) => keyword.toLowerCase() === newKeyword.toLowerCase()
				)
			) {
				blockedKeywords.push(newKeyword);
				keywordList.innerHTML =
					"<h3>屏蔽的关键词:</h3>" + blockedKeywords.join(", ");
				addKeywordInput.value = "";
				saveBlockedLists();
				initialCheck();
			}
		});

		keywordList.style.margin = "5px 0 5px 0";
		// 将元素添加到对话框
		dialog.appendChild(closeButton);
		dialog.appendChild(userList);
		dialog.appendChild(keywordList);
		dialog.appendChild(addUserInput);
		dialog.appendChild(addUserButton);
		dialog.appendChild(document.createElement("br"));
		dialog.appendChild(addKeywordInput);
		dialog.appendChild(addKeywordButton);

		// 将对话框添加到页面
		document.body.appendChild(dialog);
	}

	// 添加按钮到页面
	addBlockListButton();
})();
