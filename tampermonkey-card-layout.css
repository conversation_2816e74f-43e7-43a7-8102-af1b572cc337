/* Tampermonkey 脚本列表卡片化布局 */
/* 作者：用户定制版本 */
/* 功能：将Tampermonkey的表格布局转换为现代化的卡片布局 */

/* ========== 主容器布局 ========== */

/* 主表格容器 */
table.scripttable {
    display: block !important;
    width: 100% !important;
    background: #f5f5f5 !important;
    border-collapse: unset !important;
    table-layout: unset !important;
}

/* 表头样式 - 恢复排序功能 */
table.scripttable thead {
    display: block !important;
    width: 100% !important;
    background: #ffffff !important;
    border-bottom: 2px solid #e0e0e0 !important;
    margin-bottom: 20px !important;
    border-radius: 8px !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

table.scripttable thead tr {
    display: flex !important;
    align-items: center !important;
    padding: 12px 20px !important;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: white !important;
    border-radius: 8px !important;
    font-weight: 600 !important;
}

table.scripttable thead th {
    flex: 1 !important;
    text-align: left !important;
    padding: 8px 12px !important;
    border: none !important;
    background: transparent !important;
    color: white !important;
    font-size: 14px !important;
    font-weight: 600 !important;
}

table.scripttable thead th a {
    color: white !important;
    text-decoration: none !important;
    display: flex !important;
    align-items: center !important;
    gap: 4px !important;
    transition: all 0.3s ease !important;
}

table.scripttable thead th a:hover {
    color: #f0f0f0 !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
}

/* 排序图标样式 */
table.scripttable thead th .icon16,
table.scripttable thead th img {
    display: inline-block !important;
    width: 12px !important;
    height: 12px !important;
    margin-left: 4px !important;
    filter: brightness(0) invert(1) !important; /* 将图标变为白色 */
}

/* 排序链接的活跃状态 */
table.scripttable thead th a.active {
    color: #ffd700 !important;
    font-weight: 700 !important;
}

/* tbody 设为网格布局 */
table.scripttable tbody {
    display: grid !important;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr)) !important;
    gap: 16px !important;
    padding: 0 20px 20px 20px !important;
}

/* ========== 卡片样式 ========== */

/* 每个脚本行转为卡片 */
table.scripttable tbody tr {
    display: block !important;
    background: white !important;
    border: 1px solid #e0e0e0 !important;
    border-radius: 12px !important;
    padding: 16px !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
    transition: all 0.3s ease !important;
    margin: 0 !important;
    position: relative !important;
    min-height: 160px !important;
    overflow: visible !important;
}

/* 卡片悬停效果 */
table.scripttable tbody tr:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15) !important;
    border-color: #4CAF50 !important;
}

/* ========== 重置所有单元格样式 ========== */

/* 重置所有 td */
table.scripttable tbody tr td {
    display: block !important;
    border: none !important;
    padding: 0 !important;
    margin: 0 !important;
    width: 100% !important;
    height: auto !important;
    text-align: left !important;
    position: static !important;
    background: transparent !important;
}

/* ========== 标题行元素 ========== */

/* 第1列：复选框 - 标题行左侧 */
table.scripttable tbody tr td:nth-child(1) {
    position: absolute !important;
    top: 16px !important;
    left: 16px !important;
    width: 20px !important;
    height: 20px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    z-index: 10 !important;
}

table.scripttable tbody tr td:nth-child(1) input[type="checkbox"] {
    width: 16px !important;
    height: 16px !important;
    margin: 0 !important;
    cursor: pointer !important;
}

/* 第2列：序号 - 标题行最右侧 */
table.scripttable tbody tr td:nth-child(2) {
    position: absolute !important;
    top: 16px !important;
    right: 16px !important;
    width: 32px !important;
    height: 32px !important;
    background: rgba(255, 255, 255, 0.9) !important;
    color: #666 !important;
    border-radius: 50% !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    font-size: 12px !important;
    font-weight: 600 !important;
    z-index: 10 !important;
    border: 1px solid rgba(0, 0, 0, 0.1) !important;
}

/* 第5列：脚本名称 - 标题，左侧为复选框留出空间 */
table.scripttable tbody tr td:nth-child(5) {
    display: block !important;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: white !important;
    padding: 12px 90px 12px 44px !important;
    border-radius: 8px !important;
    font-size: 14px !important;
    font-weight: 600 !important;
    margin-bottom: 16px !important;
    text-align: left !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    white-space: nowrap !important;
    box-shadow: 0 2px 6px rgba(102, 126, 234, 0.3) !important;
    position: relative !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    width: 100% !important;
}

/* ========== 隐藏的元素 ========== */

/* 隐藏前三个非脚本项目 */
table.scripttable tbody tr:nth-child(1),
table.scripttable tbody tr:nth-child(2),
table.scripttable tbody tr:nth-child(3) {
    display: none !important;
}

/* 第3列：开关按钮列 - 保持容器但隐藏原始位置 */
table.scripttable tbody tr td:nth-child(3) {
    display: block !important;
    position: relative !important;
    height: 0 !important;
    overflow: visible !important; /* 允许绝对定位的按钮显示 */
}

/* 确保第3列中的开关按钮可见 */
table.scripttable tbody tr td:nth-child(3) input {
    visibility: visible !important;
    display: inline-block !important;
}

/* 隐藏第4列：空白列 */
table.scripttable tbody tr td:nth-child(4) {
    display: none !important;
}

/* 第6列：版本信息 - 显示为第二行橙色标签 */
table.scripttable tbody tr td:nth-child(6) {
    display: block !important;
    position: absolute !important;
    top: 68px !important;
    left: 150px !important;
    font-size: 12px !important;
    background: #ff9f7a !important;
    color: white !important;
    padding: 4px 8px !important;
    border-radius: 12px !important;
    font-weight: 500 !important;
    z-index: 2 !important;
    min-width: 30px !important;
    text-align: center !important;
}

/* 第7列：文件大小 - 显示为第二行中间 */
table.scripttable tbody tr td:nth-child(7) {
    display: block !important;
    position: absolute !important;
    top: 70px !important;
    left: 220px !important;
    font-size: 14px !important;
    color: #666 !important;
    font-weight: 500 !important;
    z-index: 2 !important;
}

/* 第8列：操作按钮列 - 显示为第三行 */
table.scripttable tbody tr td:nth-child(8) {
    display: flex !important;
    position: absolute !important;
    top: 110px !important;
    left: 16px !important;
    right: 16px !important;
    height: 40px !important;
    z-index: 4 !important;
    align-items: center !important;
    justify-content: space-between !important;
    gap: 8px !important;
}

/* 确保第8列中的所有元素都显示 */
table.scripttable tbody tr td:nth-child(8) * {
    display: inline-flex !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* 操作按钮区域 - 显示为第三行 */
/* 基于观察到的结构，操作按钮包括开关、编辑、删除等 */

/* 创建第三行容器来放置所有操作按钮 */
table.scripttable tbody tr {
    position: relative !important;
}

/* 第9列：最后更新时间 - 显示为第二行右侧 */
table.scripttable tbody tr td:nth-child(9) {
    display: block !important;
    position: absolute !important;
    top: 70px !important;
    right: 16px !important;
    font-size: 14px !important;
    color: #666 !important;
    font-weight: 500 !important;
    z-index: 2 !important;
}

/* 隐藏第10列 */
table.scripttable tbody tr td:nth-child(10) {
    display: none !important;
}

/* 隐藏表头第10列 */
table.scripttable thead th:nth-child(10) {
    display: none !important;
}

/* 第11列：操作按钮列 - 保持容器但隐藏原始位置 */
table.scripttable tbody tr td:nth-child(14) {
    display: block !important;
    position: relative !important;
    height: 0 !important;
    overflow: visible !important; /* 允许绝对定位的按钮显示 */
}

/* 确保第11列中的按钮可见 */
table.scripttable tbody tr td:nth-child(14) input {
    visibility: visible !important;
    display: inline-block !important;
}

/* 隐藏第12列及以后的所有列（如果存在） */
table.scripttable tbody tr td:nth-child(n+12) {
    display: none !important;
}

/* 额外隐藏：确保不需要的图标被隐藏（但保留表头排序图标） */
/* 隐藏tbody中的.icon16图标 */
table.scripttable tbody .icon16 {
    display: none !important;
}

/* 隐藏tbody中的图片图标 */
table.scripttable tbody img {
    display: none !important;
}

/* 隐藏tbody中链接的图标 */
table.scripttable tbody a img,
table.scripttable tbody a .icon16 {
    display: none !important;
}

/* 隐藏特定的span元素（包含图标） */
table.scripttable span[id*="site"],
table.scripttable span[class*="icon"] {
    display: none !important;
}

/* ========== 第三行操作按钮样式 ========== */

/* 第3列：开关按钮 - 移动到第三行左侧第二个位置 */
table.scripttable tbody tr td:nth-child(3) input[type="button"][onclick*="setEnabled"],
table.scripttable tbody tr td:nth-child(3) input[onclick*="setEnabled"] {
    position: absolute !important;
    top: 95px !important;
    left: 74px !important; /* 编辑按钮(50px) + 间距(8px) + 边距(16px) */
    width: 60px !important;
    height: 32px !important;
    border-radius: 16px !important;
    border: 2px solid #ddd !important;
    font-size: 11px !important;
    font-weight: 600 !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    z-index: 6 !important;
}

/* 第11列：编辑按钮 - 移动到第三行左侧第一个位置 */
table.scripttable tbody tr td:nth-child(14) input[value="编辑"],
table.scripttable tbody tr td:nth-child(14) input[value="Edit"] {
    position: absolute !important;
    top: 95px !important;
    left: 16px !important;
    width: 50px !important;
    height: 32px !important;
    background: #4CAF50 !important;
    color: white !important;
    border: none !important;
    border-radius: 6px !important;
    font-size: 12px !important;
    font-weight: 600 !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    z-index: 6 !important;
}

/* 启用状态的开关按钮 */
table.scripttable tbody tr td:nth-child(3) input[value="启用"],
table.scripttable tbody tr td:nth-child(3) input[value="Enable"] {
    background: #4CAF50 !important;
    color: white !important;
    border-color: #4CAF50 !important;
}

/* 禁用状态的开关按钮 */
table.scripttable tbody tr td:nth-child(3) input[value="禁用"],
table.scripttable tbody tr td:nth-child(3) input[value="Disable"] {
    background: #f44336 !important;
    color: white !important;
    border-color: #f44336 !important;
}

/* 第11列：删除按钮 - 移动到第三行右侧 */
table.scripttable tbody tr td:nth-child(14) input[value="删除"],
table.scripttable tbody tr td:nth-child(14) input[value="Delete"] {
    position: absolute !important;
    top: 95px !important;
    right: 16px !important;
    width: 50px !important;
    height: 32px !important;
    background: #f44336 !important;
    color: white !important;
    border: none !important;
    border-radius: 6px !important;
    font-size: 12px !important;
    font-weight: 600 !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    z-index: 6 !important;
}

/* 通用按钮悬停效果（作为备用） */
table.scripttable tbody tr input[type="button"]:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2) !important;
}

/* 第11列编辑按钮悬停效果 */
table.scripttable tbody tr td:nth-child(14) input[value="编辑"]:hover,
table.scripttable tbody tr td:nth-child(14) input[value="Edit"]:hover {
    background: #45a049 !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2) !important;
}

/* 第11列删除按钮悬停效果 */
table.scripttable tbody tr td:nth-child(14) input[value="删除"]:hover,
table.scripttable tbody tr td:nth-child(14) input[value="Delete"]:hover {
    background: #da190b !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2) !important;
}

/* 第3列开关按钮悬停效果 */
table.scripttable tbody tr td:nth-child(3) input[onclick*="setEnabled"]:hover {
    transform: translateY(-1px) !important;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15) !important;
}

/* 禁用状态的按钮样式 */
table.scripttable tbody tr input[type="button"]:disabled {
    opacity: 0.6 !important;
    cursor: not-allowed !important;
}

/* 确保按钮文字不被截断 */
table.scripttable tbody tr input[type="button"] {
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
}

/* ========== 状态样式 ========== */

/* 选中状态 */
table.scripttable tbody tr.selected {
    border-color: #667eea !important;
    background: #f8f9ff !important;
    box-shadow: 0 4px 16px rgba(102, 126, 234, 0.2) !important;
}

/* 禁用状态 */
table.scripttable tbody tr:not(.enabled) {
    opacity: 0.7 !important;
}

table.scripttable tbody tr:not(.enabled) td:nth-child(5) {
    background: linear-gradient(135deg, #95a5a6 0%, #7f8c8d 100%) !important;
}

/* ========== 响应式设计 ========== */

@media (max-width: 1200px) {
    table.scripttable {
        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr)) !important;
        gap: 12px !important;
        padding: 16px !important;
    }
}

@media (max-width: 768px) {
    table.scripttable {
        grid-template-columns: 1fr !important;
        padding: 12px !important;
    }
}

/* ========== 容器修复 ========== */

/* 确保容器宽度 */
.tv_content,
.section_content {
    width: 100% !important;
    overflow: visible !important;
}

/* 修复布局问题 */
table.scripttable * {
    box-sizing: border-box !important;
}

/* 清除原有样式 */
table.scripttable,
table.scripttable tbody,
table.scripttable tbody tr,
table.scripttable tbody tr td {
    border-collapse: unset !important;
    border-spacing: 0 !important;
    background-image: none !important;
}
