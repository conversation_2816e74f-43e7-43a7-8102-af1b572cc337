// ==UserScript==
// @name         逛色花(适配新UI版）
// @description  最好用的98堂(原色花堂)脚本 高级搜索 自动签到 快速复制 快速评分 划词搜索 图片预览 快速收藏
// <AUTHOR>
// @version      2.0
// @match        *://*.sehuatang.net/*
// @match        *://*.sehuatang.org/*
// @match        *://*.sehuatang.*/*
// @grant        GM_registerMenuCommand
// @grant        GM_getValue
// @grant        GM_setValue
// ==/UserScript==

(function () {
	"use strict";
	/* global showWindow */
	/* global stopRandomSelection */
	/* global setanswer */

	// #region 全局变量
	if (window.self !== window.top) {
		return;
	}
	/**
	 * activeTooltips: 用于记录当前页面中活动的工具提示数量。这可以用于管理和控制页面上显示的提示。
	 */
	let activeTooltips = 0;

	/**
	 * DEFAULT_TID_OPTIONS: 存储默认的板块列表。
	 * 每个板块都有一个唯一的value和一个对应的label。
	 * 这个常量可以被用于下拉列表、搜索过滤等。
	 */
	const DEFAULT_TID_OPTIONS = [
		{ value: 95, label: "综合区" },
		{ value: 166, label: "AI区" },
		{ value: 141, label: "原创区" },
		{ value: 142, label: "转帖区" },
		{ value: 97, label: "出售区" },
		{ value: 143, label: "悬赏区" },
		{ value: 36, label: "亚洲无码" },
		{ value: 37, label: "亚洲有码" },
		{ value: 103, label: "中文字幕" },
		{ value: 151, label: "4K原版" },
		{ value: 145, label: "原档自提字幕区" },
		{ value: 146, label: "原档自译字幕区" },
		{ value: 121, label: "原档字幕分享区" },
		{ value: 159, label: "原档新作区" },
	];

	/**
	 * baseURL: 获取当前页面的主机URL，用于构建其他URL。
	 */
	const baseURL = `https://${window.location.host}`;

	// #endregion

	// #region 获取用户设置

	/**
	 * 获取用户设置。
	 * 从用户脚本的存储中检索各种设置，并为每个设置返回其值或默认值。
	 * 这些设置可以用于改变脚本的行为、外观和功能。
	 *
	 * @returns {Object} 返回一个对象，该对象包含了所有的用户设置。
	 */
	function getSettings() {
		/**
		 * 从脚本存储中获取JSON值。
		 * 如果检索到的值不是有效的JSON，则返回默认值。
		 *
		 * @param {string} key - 存储的键名。
		 * @param {string} defaultValue - 如果无法检索到或解析值，则返回的默认JSON值。
		 * @returns {any} 返回解析的JSON值或默认值。
		 */
		const getJSONValue = (key, defaultValue) => {
			const value = GM_getValue(key, defaultValue);
			try {
				return JSON.parse(value);
			} catch {
				return JSON.parse(defaultValue);
			}
		};

		return {
			logoText: GM_getValue("logoText", ""), // 评分文字和特效文字
			tipsText: GM_getValue("tipsText", ""), // 评分文字和特效文字
			imageSize: GM_getValue("imageSize", "50px"), // 图片的大小
			imageUrl: GM_getValue("imageUrl","/uc_server/data/avatar/000/46/26/94_avatar_big.jpg"), // 图片的URL
			blockMedals: GM_getValue("blockMedals", 1), // 隐藏勋章设置，默认为0（不隐藏）
			excludeGroup: getJSONValue("excludeGroup", "[]"), // 要排除的组
			TIDGroup: getJSONValue("TIDGroup", "[]"), // TID分组
			displayBlockedTips: GM_getValue("displayBlockedTips", false), // 是否显示屏蔽黑名单提示
			autoPagination: GM_getValue("autoPagination", true), // 是否开启自动分页
			showImageButton: GM_getValue("showImageButton", "show"), // 是否显示图片
			excludeOptions: GM_getValue("excludeOptions", []), // 要排除的选项
			excludePostOptions: GM_getValue("excludePostOptions", []), // 要排除的选项
			blockedUsers: GM_getValue("blockedUsers", []), // 被屏蔽的用户
			orderFids: getJSONValue("orderFids", "[]"), // FID的顺序
			showAvatar: GM_getValue("showAvatar", true), // 是否显示用户头像
			maxGradeThread: GM_getValue("maxGradeThread", 10),
			displayThreadImages: GM_getValue("displayThreadImages", false), // 是否显示图片预览
			showDown: GM_getValue("showDown", true), // 是否显示下载附件
			showCopyCode: GM_getValue("showCopyCode", true), // 是否显示复制代码
			showFastPost: GM_getValue("showFastPost", true), // 是否显示快速发帖
			showFastReply: GM_getValue("showFastReply", true), // 是否显示快速回复
			showQuickGrade: GM_getValue("showQuickGrade", true), // 是否显示快速评分
			showQuickStar: GM_getValue("showQuickStar", true), // 是否显示快速收藏
			showClickDouble: GM_getValue("showClickDouble", true), // 是否显示一键二连
			showViewRatings: GM_getValue("showViewRatings", true), // 是否显示查看评分
			showPayLog: GM_getValue("showPayLog", true), // 是否显示购买记录
			showFastCopy: GM_getValue("showFastCopy", true), // 是否显示复制帖子
			blockingResolved: GM_getValue("blockingResolved", true),
			blockingIndex: GM_getValue("blockingIndex", false), // 是否屏蔽首页热门
			showToggleImageButton: GM_getValue("showToggleImageButton", true), // 是否显示隐藏/显示图片按钮
		};
	}

	// #endregion

	// #region 样式

	/**
	 * 添加自定义样式到页面中。
	 * 此函数会创建一个<style>元素，并定义了一些自定义的CSS样式，然后将其添加到文档的头部。
	 */

	function addStyles() {
		const style = document.createElement("style");
		style.innerHTML = `
/* --- 通用自定义按钮的样式 --- */
* {
  font-family: "PingFang SC","Noto Sans SC","Microsoft YaHei","SF Pro", Arial, sans-serif !important;
}

a {
    text-decoration: none !important;
}

/* --- 屏蔽卡片的强制隐藏样式 --- */
.thread-card.blocked-card {
    display: none !important;
    visibility: hidden !important;
    height: 0 !important;
    overflow: hidden !important;
    margin: 0 !important;
    padding: 0 !important;
    border: none !important;
    opacity: 0 !important;
    position: absolute !important;
    left: -9999px !important;
}

.bgsh-customBtn, .bgsh-searchBtn, .bgsh-quickReplyToPostBtn, .bgsh-QuickMiscReportBtn, .bgsh-quickReportadToPostBtn, .bgsh-quickTopicadminToPostBtn, .bgsh-quickGradeToPostBtn, .bgsh-openAllUrlBtn, .bgsh-fastPMButtonBtn, .bgsh-quickReplyEditToPostBtn {
	padding: 8px 15px;
	margin-bottom: 8px;
	margin-right: 8px;
	width: 100%;
	outline: none;
	white-space: pre-line;
	font-size: 14px;
	font-weight: 500 !important;
	color: #fff;
	border-radius: 12px;
	background: var(--button) !important;
	border: none;
	backdrop-filter: blur(10px) saturate(150%);
	box-shadow: 0 1px 5px rgba(0, 0, 0, 0.1);
	cursor: pointer;
}

  /* quickTopicadminToPostBtn按钮的设置 */
.bgsh-quickReplyToPostBtn, .bgsh-quickTopicadminToPostBtn, .bgsh-quickReplyEditToPostBtn {
	width: auto;
	float: right;
	box-shadow: none  !important;
	font-size: 13px;
	margin-top: 5px;
	color: var(--primary-color);
	background-color: transparent !important;
}

  /* quickGradeToPostBtn按钮的设置 */
  .bgsh-quickGradeToPostBtn,
  .bgsh-QuickMiscReportBtn,
  .bgsh-quickReportadToPostBtn {
    width: auto;
    float: left;
    box-shadow: none;
    font-size: 13px;
    margin-top: 5px;
    color: var(--primary-color);
    background-color: transparent !important;
  }

  /* quickGradeToPostBtn按钮的设置 */
  .bgsh-fastPMButtonBtn {
    width: auto;
    float: left;
  }

  /* openAllUrlBtn按钮的设置 */
  .bgsh-openAllUrlBtn {
    width: 100px;
    font-size: 16px;
    padding: 0;
    box-shadow: 0 1px 10px rgba(0, 0, 0, 0.1);
  }

  #quickTopicadminToPost,
   #quickReplyToPost,
   #quickMiscReport,
   #quickReportadToPost {
    	box-shadow: none !important;
}

  /* 按钮的最大宽度设置 */
  .bgsh-searchBtn {
    max-width: 400px;
    background-color: #E45E6B;
  }

  /* 按钮的悬停效果 */
  .bgsh-customBtn:hover,
  .bgsh-searchBtn:hover,
  .bgsh-quickGradeToPostBtn:hover,
  .bgsh-quickReplyToPostBtn:hover,
  .bgsh-QuickMiscReportBtn:hover,
  .bgsh-quickReportadToPostBtn:hover,
  .bgsh-fastPMButtonBtn:hover,
  .bgsh-quickReplyEditToPostBtn:hover {
    text-shadow: 0 0px 5px rgba(255, 255, 255, 0.4) ;
    transition: all 0.2s ease;
  }

  /* --- 自定义的搜索框样式 --- */
.advanced-search {
	position: fixed;
	right: calc(0px + 1vh);
	top: 140px;
	border-radius: 10px;
	z-index: 1000;
	box-shadow: 0 1px 20px rgba(0, 0, 0, 0.08);
	background: rgba(255, 255, 255, 0.8);
	padding: 20px;
	display: grid;
	grid-template-columns: auto auto;
	column-gap: 20px;
	backdrop-filter: blur(20px) saturate(180%) !important;
	border: 1px solid rgba(255, 255, 255, 0.5) !important;
}

  /* 添加间距 */
  .advanced-search .bgsh-forget {
    max-height: 580px; /* 设置最大高度限制 */
    overflow: visible; /* 确保超出部分内容可见 */
    display: flex;
    flex-wrap: wrap;
    flex-direction: column;
    font-size: 13px;
  }

  /* --- 复选框样式 --- */
  .bgsh-forget .bgsh-checkbox-label {
    display: block;
    position: relative;
    cursor: pointer;
    font-size: 22px;
    line-height: 22px;
    margin-right: 10px;
  }

  .bgsh-label-text {
    display: inline-block;
    font-weight: 500;
    left: 12%;
    font-size: 13px;
  }

  .bgsh-forget .bgsh-checkbox-label input {
    opacity: 0;
    cursor: pointer;
  }

  /* 复选框的自定义样式 */
  .bgsh-checkbox-label .bgsh-checkbox-custom {
    position: absolute;
    top: 8px;
    left: 0;
    height: 12px;
    width: 12px;
    background-color: rgba(0, 0, 0, 0.1);
    border-radius: 40px;
    border: none;
    box-shadow: none;
  }

  /* 复选框选中状态样式 */
  .bgsh-checkbox-label input:checked ~ .bgsh-checkbox-custom {
    box-shadow: none;
    background-color: #53C41B;
  }

  /* 复选框标志样式 */
  .bgsh-checkbox-label .bgsh-checkbox-custom::after {
    position: absolute;
    content: "";
    left: 10px;
    top: 10px;
    height: 0;
    width: 0;
    border-radius: 5px;
    border: solid #635f5f;
    border-width: 0 3px 3px 0;
    transform: rotate(0deg) scale(0);
    opacity: 1;
    transition: all 0.3s ease-out;
  }

  /* 复选框选中状态下的标志样式 */
  .bgsh-checkbox-label input:checked ~ .bgsh-checkbox-custom::after {
    position: absolute;
    top: 0;
    left: 0;
    height: 12px;
    width: 12px;
    background-color: #53C41B;
    border-radius: 40px;
    border: none;
    box-shadow: none;
    transition: none;
  }

  /* 自定义复选框的indeterminate标志样式 */
  .bgsh-checkbox-label .bgsh-checkbox-custom::before {
    position: absolute;
    content: "";
    left: 1px;
    top: 5px;
    width: 10px;
    height: 2px;
    background-color: #848484;
    opacity: 0;
    transition: none;
  }

  /* 当复选框处于indeterminate状态时的样式 */
  .bgsh-checkbox-label input:indeterminate ~ .bgsh-checkbox-custom::before {
    opacity: 1;
  }

  .bgsh-dateInput {
    border: 1px solid #d4d4d4;
    background-color: #fff;
    transition: border 0.3s;
    margin: 0 5px;
    width: 150px;
  }

  /* 聚焦时的样式 */
  .bgsh-dateInput:focus {
    border-color: #007BFF;
    outline: none;
    box-shadow: 0 0 5px rgba(0, 123, 255, 0.2);
  }
  /* 鼠标悬浮时的样式 */
  .bgsh-dateInput:hover {
    border-color: #b3b3b3;
  }
  .bgsh-watermark-wrapper {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none; /* 确保水印不会阻止按钮点击等交互 */
  }
  .bgsh-watermark-text {
    position: absolute;
    text-align: center;
    font-size: 30px;
    color: #000;
    font-weight: bold;
    overflow: hidden;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: row;
  }
  .bgsh-watermark-text .icon {
    width: 30px;
    height: 30px;
    fill: red;
    margin: 0 5px; /* 修改图标的间距 */
  }
  /* 默认状态下的按钮样式 */
  .close-btn {
      position: absolute;
      top: 10px;
      right: 10px;
      cursor: pointer;
      width: 20px;
      height: 20px;
      border-radius: 50%;
      border: none;
      background-color: #000;
      transition: 0.15s;
      box-shadow: 0 1px 6px rgba(0, 0, 0, 0.15);
  }
  /* hover 状态下的背景色变化 */
  .close-btn:hover {
      background-color: red;  /* hover 时背景色变为红色 */
      color: white;  /* 设置文字颜色 */
  }
  /* 交叉的线条 */
  .close-btn span {
      width: 11px; /* 确保线条占有一定大小，避免点击失效 */
      height: 2px;
      background: white;
      position: absolute;
      top: 50%;
      left: 50%;
      z-index: 999;
      transform: translate(-50%, -50%);
      pointer-events: none;  /* 确保点击事件不被这条线拦截 */
  }
  .close-btn span:nth-child(1) {
      transform: translate(-50%, -50%) rotate(45deg);  /* 第一条斜线 */
  }
  .close-btn span:nth-child(2) {
      transform: translate(-50%, -50%) rotate(-45deg);  /* 第二条斜线 */
  }
  #insetBtn {
      border: none;
      cursor: pointer;
      border-radius: 6px;
      margin: 10px 0 0 7px;
      background-color: #000;
      color: #fff;
      box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
      height: 25px;
      width: 55px;
      font-size: 13px;
  }

  input[type="radio"] {
      accent-color: #e45e6b;
      transition: all 0.2s ease;
      vertical-align: text-top;
  }
  input[type="radio"]:hover {
      border: 2px solid #e45e6b;
      background-color: #e45e6b;
  }

  /* 附件 */
#customModal {
	position: fixed;
	left: 50%;
	top: 50%;
	transform: translate(-36%, -50%) scale(1);
	padding: 30px 10px;
	border-radius: 12px;
	width: 560px;
	z-index: 100;
	background: rgba(240, 242, 244, 0.85);
  backdrop-filter: blur(30px) saturate(180%);
	border: 1px solid rgba(255, 255, 255, 0.8);
	box-shadow: 0 1px 50px rgba(0, 0, 0, 0.20), inset 0px 0px 60px 20px rgba(255, 255, 255, 0.6) ;

}

#customModal > div {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
}
#customModal > div > dl {
	display: flex;
	padding: 10px;
	width: 500px;
	height: auto;
	overflow: visible;
	align-items: center;
	margin: 10px;
	flex-direction: row;
	border-radius: 8px;
	background-color: rgb(131, 133, 135, 0.15);
}
#customModal > div > span {
    padding: 15px;
    display: block;
    margin: 10px 20px;
    background: #e0e0e070;
    border-radius: 8px;
}
#customModal > div > span a {
    color: #f44646 !important;
    font-size: 13px;
}
.tattl strong,
.tattl a {
    color: #f44646;
}
#customModal .xg1 {
    color: #333 !important;
}
.tattl dt {
    padding: 0!important;
    margin-right: 5px;
    width: 48px;
    height: 48px;
    line-height: 48px;
}
  /* 关闭按钮样式 */
#closeModal {
    float: right;
    width: 18px;
    height: 18px;
    overflow: hidden;
    text-indent: -9999px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
    border-radius: 50%;
    background-color: rgba(0,0,0,1);
    border: none;
    color: #fff;
    position: absolute;
    top: 15px;
    cursor: pointer;
    right: 15px;
    transition: background 0.15s ease;
}
  /* 关闭按钮悬停时的效果 */
  #closeModal:hover {
      background: red;
  }
  /* 伪元素，用来画出"X" */
  #closeModal::before,
  #closeModal::after {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      width: 10px; /* X线条长度 */
      height: 2px; /* X线条粗细 */
      background: white; /* X线条颜色 */
      transform: translate(-50%, -50%) rotate(45deg); /* 旋转45度 */
      transition: background 0.15s ease; /* 过渡效果 */
  }
  /* 第二条斜线 */
  #closeModal::after {
      transform: translate(-50%, -50%) rotate(-45deg); /* 旋转-45度 */
  }
  /* 附件弹出框 */
  #fwin_attachpay {
    position: fixed;
    z-index: 300;
    box-shadow: 0 2px 40px rgba(0, 0, 0, 0.20);
    left: 50%;
    top: 50%;
    border: 0px solid #ddd;
    transform: translate(-50%, -50%);
    backdrop-filter: blur(50px);
    background-color: #ffffffed;
  }
  #customModal > div > dl > dd > p.attnm > a {
     font-size: 13px;
  }
  .tattl dt {
      padding: 10px 20px 10px 10px;
  }
  .tattl dt img {
      width: 48px;
      height: 48px;
  }
  .tattl dd {
    margin-left: 0px;
    color: #222;
    overflow: hidden;
  }
  .attnm {
    height: auto;
    margin-bottom: 0;
    overflow: hidden;
    white-space: normal;
   }
  .attm dt {
      display: none;
   }
  #resourceName,
  #resourceSize,
  #videoCount,
  #imageCount,
  #quota,
  #resourceLink {
     border: 1px solid #666;
     border-radius: 6px;
     margin-left: 4px;
  }
  #resourceName,
  #resourceLink {
      width: 300px;
  }
  #organizeModal {
    line-height: 2.5;
    font-size: 13px;
    width: 650px;
    border: none;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%) scale(1);
    border-radius: 10px;
    padding: 20px;
    z-index: 1000;
    position: fixed;
    display: none;
    cursor: move;
    user-select: none;
  backdrop-filter: blur(30px) saturate(180%);
	border: 1px solid rgb(255, 255, 255);
	box-shadow: 0 1px 50px rgba(0, 0, 0, 0.20), inset 0px 0px 90px 10px rgba(255, 255, 255, 0.9);
	background-color: rgba(244, 246, 248, 0.85) ;
}
#imagePreviewTbody {
	display: flex;
	width: 100%;
	overflow: hidden;
}

#imagePreviewTbody tr {
	background: rgba(0, 0, 0, 0.1);
	width: 100%;
}

#imagePreviewTbody td {
	width: 100%;
	padding: 0;
}

#imagePreviewTbody div {
    gap: 10px;
    padding: 0 5px;
    display: flex;
    width: 100%;
    overflow: hidden;
}
#imagePreviewTbody img {
	margin-right: 10px !important;
	object-fit: cover;
	height: 300px;
	width: auto;
	max-width: 300px;
	flex-shrink: 0;
}

/* 新布局图片预览样式 */
.thread-image-preview {
    display: flex !important;
    margin: 15px 0 0 6px !important;
    max-height: 200px !important;
    white-space: nowrap !important;
    gap: 10px !important;
}

.thread-image-preview img {
	object-fit: cover !important;
	height: auto !important;
	width: auto !important;
	max-width: 250px !important;
	flex-shrink: 0 !important;
	cursor: pointer !important;
	border: 4px solid rgba(0, 0, 0, 0.1) !important;
	max-height: 200px;
}

/* 禁用置顶贴的图片预览 */
/* 新布局置顶贴 - 通过class识别 */
.thread-card.sticky .thread-image-preview {
	display: none !important;
}

#shareBtn, #organizeBtn {
  color: #fff;
  background-color: var(--button);
  border: none;
  height: 25px;
  width: 50px;
  margin-left: 20px;
  border-radius: var(--icon-radius) !important;
}

/* 快速发帖模态框样式 */
#postModal {
	position: fixed;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%) scale(1);
	padding: 0;
	width: 100%;
	height: 100%;
	z-index: 1000;
	background: var(--background-color);
	backdrop-filter: blur(100px) saturate(50%);
	overflow: hidden;
}

#postModal .close-btn {
	position: absolute;
	top: 15px;
	right: 15px;
	cursor: pointer;
	width: 25px;
	height: 25px;
	border-radius: 50%;
	border: none;
	background-color: #000;
	transition: 0.15s;
	box-shadow: 0 1px 6px rgba(0, 0, 0, 0.15);
	z-index: 1001;
}

#postModal .close-btn:hover {
	background-color: red;
}

#postModal .close-btn::before,
#postModal .close-btn::after {
	content: '';
	position: absolute;
	top: 50%;
	left: 50%;
	width: 12px;
	height: 2px;
	background: white;
	transform: translate(-50%, -50%) rotate(45deg);
	transition: background 0.15s ease;
}

#postModal .close-btn::after {
	transform: translate(-50%, -50%) rotate(-45deg);
}


`;

		document.head.appendChild(style);
	}

	// #endregion

	// #region 消息提示

	/**
	 * 在页面中显示一个淡出的提示消息。
	 * 使用该函数可以在页面中临时显示一个提示消息，该消息在一段时间后会自动淡出并消失。
	 *
	 * @param {string} message - 需要显示的提示消息内容。
	 */
	function showTooltip(message) {
		const tooltip = document.createElement("span");

		// 设定提示消息的初始样式
		const tooltipStyles = {
			position: "fixed",
			top: `calc(33.33% + ${activeTooltips * 80}px)`,
			left: "50%",
			transform: "translate(-50%, -50%)",
			backgroundColor: "rgba(0, 0, 0, 0.7)",
      backdropFilter: "blur(10px) saturate(180%)",
      boxShadow: "0 1px 50px rgba(0, 0, 0, 0.3)",
			color: "#fff",
			padding: "20px 40px",
			borderRadius: "12px",
			zIndex: "1000",
			transition: "opacity 0.5s",
			fontSize: "20px",
		};
		var settings = getSettings();
		Object.assign(tooltip.style, tooltipStyles); // 使用Object.assign将样式批量应用到元素上
		tooltip.innerText = `${settings.tipsText}` + message;
		document.body.appendChild(tooltip);

		activeTooltips++;

		// 淡出效果函数
		const fadeOut = (element, duration = 500) => {
			let opacity = 1;
			const timer = setInterval(function () {
				opacity -= 50 / duration;
				if (opacity <= 0) {
					clearInterval(timer);
					element.style.display = "none"; // or "hidden", depending on the desired final state
				} else {
					element.style.opacity = opacity;
				}
			}, 50);
		};

		setTimeout(() => {
			fadeOut(tooltip);
			setTimeout(() => {
				document.body.removeChild(tooltip);
				activeTooltips--;
			}, 500);
		}, 2000);
	}
	// #endregion

	// #region Tampermonkey 菜单命令注册
	/**
	 * 注册一个命令到Tampermonkey的上下文菜单，允许用户访问"逛色花"的设置界面。
	 */
	GM_registerMenuCommand("逛色花设置", () => {
		createSettingsUI(getSettings());
	});
	// #endregion

	// #region 设置界面HTML构造

	/**
	 * 生成"逛色花"设置界面的HTML内容
	 * @param {Object} settings - 当前的设置数据
	 * @returns {string} - 设置界面的HTML字符串
	 */
	function generateSettingsHTML(settings) {
		return `
    <div class='bgsh-setting-box'>
        <div class='bgsh-setting-box-container '>
            <div class="bgsh-setting-first">
                <label for="tipsText">提示文字</label>
                <br>
                <input type="text" id="tipsTextInput" value="${
									settings.tipsText
								}">
                <br>
                <label for="logoText">评分文字</label>
                <br>
                <input type="text" id="logoTextInput" value="${
									settings.logoText
								}">
                <br>
                <label for="maxGradeThread">主贴评分最大值:</label>
                <input id="maxGradeThread" value="${settings.maxGradeThread}">
                <br>
                <fieldset>
                    <label>隐藏勋章</label>
                    <br>
                    <label>
                        <input type="radio" name="blockMedals" value="0" ${
													settings.blockMedals === 0 ? "checked" : ""
												}>
                        不隐藏
                    </label>
                    <label>
                        <input type="radio" name="blockMedals" value="1" ${
													settings.blockMedals === 1 ? "checked" : ""
												}>
                        隐藏所有
                    </label>
                    <label>
                        <input type="radio" name="blockMedals" value="2" ${
													settings.blockMedals === 2 ? "checked" : ""
												}>
                        隐藏女优勋章
                    </label>
                </fieldset>
                <label class='bgsh-setting-checkbox-label' for="displayBlockedTipsCheckbox">
                    <input type='checkbox' id="displayBlockedTipsCheckbox" ${
											settings.displayBlockedTips ? "checked" : ""
										}>
                    <span class='bgsh-setting-checkbox-custom'></span>
                    <span>显示黑名单屏蔽提示</span>
					<br>
                </label>
                <label class='bgsh-setting-checkbox-label' for="showAvatarCheckbox">
                    <input type='checkbox' id="showAvatarCheckbox" ${
											settings.showAvatar ? "checked" : ""
										}>
                    <span class='bgsh-setting-checkbox-custom'></span>
                    <span>显示用户头像</span>
                </label>
                <br>
                <label class='bgsh-setting-checkbox-label' for="autoPaginationCheckbox">
                    <input type='checkbox' id="autoPaginationCheckbox" ${
											settings.autoPagination ? "checked" : ""
										}>
                    <span class='bgsh-setting-checkbox-custom'></span>
                    <span>启用自动翻页</span>
                </label>
                <br>
                <label class='bgsh-setting-checkbox-label' for="displayThreadImagesCheckbox" >
                <input type='checkbox' id="displayThreadImagesCheckbox" ${
									settings.displayThreadImages ? "checked" : ""
								}>
                    <span class='bgsh-setting-checkbox-custom'></span>
                    <span>启用图片预览</span>
                </label>
                <br>
                <label class='bgsh-setting-checkbox-label' for="showDownCheckbox">
                <input type='checkbox' id="showDownCheckbox" ${
									settings.showDown ? "checked" : ""
								}>
                    <span class='bgsh-setting-checkbox-custom'></span>
                    <span>启用下载附件</span>
                </label>
                <br>
                <label class='bgsh-setting-checkbox-label' for="showCopyCodeCheckbox">
                <input type='checkbox' id="showCopyCodeCheckbox" ${
									settings.showCopyCode ? "checked" : ""
								}>
                    <span class='bgsh-setting-checkbox-custom'></span>
                    <span>启用复制代码</span>
                </label>
                <br>
                <label class='bgsh-setting-checkbox-label' for="showFastPostCheckbox">
                <input type='checkbox' id="showFastPostCheckbox" ${
									settings.showFastPost ? "checked" : ""
								}>
                    <span class='bgsh-setting-checkbox-custom'></span>
                    <span>启用快速发帖</span>
                </label>
                <br>
                <label class='bgsh-setting-checkbox-label' for="showFastReplyCheckbox">
                <input type='checkbox' id="showFastReplyCheckbox" ${
									settings.showFastReply ? "checked" : ""
								}>
                    <span class='bgsh-setting-checkbox-custom'></span>
                    <span>启用快速回复</span>
                </label>
                <br>
                <label class='bgsh-setting-checkbox-label' for="showQuickGradeCheckbox">
                <input type='checkbox' id="showQuickGradeCheckbox" ${
									settings.showQuickGrade ? "checked" : ""
								}>
                    <span class='bgsh-setting-checkbox-custom'></span>
                    <span>启用快速评分</span>
                </label>
                <br>
                <label class='bgsh-setting-checkbox-label' for="showQuickStarCheckbox">
                <input type='checkbox' id="showQuickStarCheckbox" ${
									settings.showQuickStar ? "checked" : ""
								}>
                    <span class='bgsh-setting-checkbox-custom'></span>
                    <span>启用快速收藏</span>
                </label>
                <br>
                <label class='bgsh-setting-checkbox-label' for="showClickDoubleCheckbox">
                <input type='checkbox' id="showClickDoubleCheckbox" ${
									settings.showClickDouble ? "checked" : ""
								}>
                    <span class='bgsh-setting-checkbox-custom'></span>
                    <span>启用一键二连</span>
                </label>
                <br>
                <label class='bgsh-setting-checkbox-label' for="showViewRatingsCheckbox">
                <input type='checkbox' id="showViewRatingsCheckbox" ${
									settings.showViewRatings ? "checked" : ""
								}>
                    <span class='bgsh-setting-checkbox-custom'></span>
                    <span>启用查看评分</span>
                </label>
                <br>
                <label class='bgsh-setting-checkbox-label' for="showToggleImageButtonCheckbox">
                <input type='checkbox' id="showToggleImageButtonCheckbox" ${
									settings.showToggleImageButton ? "checked" : ""
								}>
                    <span class='bgsh-setting-checkbox-custom'></span>
                    <span>启用隐藏/显示图片</span>
                </label>
                <br>
                <label class='bgsh-setting-checkbox-label' for="showFastCopyCheckbox">
                <input type='checkbox' id="showFastCopyCheckbox" ${
									settings.showFastCopy ? "checked" : ""
								}>
                    <span class='bgsh-setting-checkbox-custom'></span>
                    <span>启用复制帖子</span>
                </label>
                <br>
                <label class='bgsh-setting-checkbox-label' for="blockingIndexCheckbox">
                <input type='checkbox' id="blockingIndexCheckbox" ${
									settings.blockingIndex ? "checked" : ""
								}>
                    <span class='bgsh-setting-checkbox-custom'></span>
                    <span>屏蔽首页热门</span>
                </label>
                <br>
            </div>
            <div class="bgsh-setting-second">
                <label for="excludeOptionsTextarea">高级搜索排除关键字（每行一个）:</label>
                <br>
                <textarea id="excludeOptionsTextarea">${settings.excludeOptions.join(
									"\n"
								)}</textarea>
                <br>
                <label for="blockedUsersList">黑名单屏蔽的用户名（每行一个）：</label>
                <br>
                <textarea id="blockedUsersList">${settings.blockedUsers.join(
									"\n"
								)}</textarea>
                <br>
                <label for="excludePostOptionsTextarea">帖子列表页黑名单关键字（每行一个）:</label>
                <br>
                <textarea id="excludePostOptionsTextarea">${settings.excludePostOptions.join(
									"\n"
								)}</textarea>
                <br>
            </div>
        </div>
        <div class="bgsh-setting-button">
            <button id="saveButton">保存</button>
            <button id="closeButton">关闭</button>
        </div>
    </div>
    `;
	}

	// #endregion

	// #region 设置界面生成与事件绑定

	/**
	 * 创建并显示设置界面
	 * @param {Object} settings - 当前的设置数据
	 */
	function createSettingsUI(settings) {
		// 若之前的设置界面存在，先进行移除
		const existingContainer = document.getElementById("settingsUIContainer");
		if (existingContainer) {
			existingContainer.remove();
		}

		// 添加设置界面的样式
		generateSettingsStyles();

		// 根据当前设置生成界面内容
		const containerHTML = generateSettingsHTML(settings);

		// 创建界面容器，并添加到页面
		const container = document.createElement("div");
		container.id = "settingsUIContainer";
		container.innerHTML = containerHTML;
		document.body.appendChild(container);

		// 添加拖动功能
		enableDrag(container);

		// 为"保存"和"关闭"按钮绑定事件
		const saveButton = document.getElementById("saveButton");
		const closeButton = document.getElementById("closeButton");

		// 保存按钮点击后，保存设置并隐藏界面
		saveButton.addEventListener("click", function () {
			saveSettings(settings);
			container.style.display = "none"; // 只隐藏容器，不从DOM移除
		});

		// 关闭按钮点击后，直接隐藏界面
		closeButton.addEventListener(
			"click",
			() => (container.style.display = "none") // 只隐藏容器，不从DOM移除
		);
	}

	// #endregion

	// #region 设置界面样式

	/**
	 * 生成并应用设置界面的样式
	 */
	function generateSettingsStyles() {
		const style = `
/* 通用样式，应用于所有元素 */
#settingsUIContainer * {
  font-size: 13px;
  font-weight: 500  !important;
  color: #222;
  box-sizing: border-box;
  margin: 0px;
  padding: 0px;
  border-radius: 10px;
}

#settingsUIContainer {
	position: fixed;
	top: 50%;
	left: 50%;
	border-radius: 12px;
	width: 600px;
	z-index: 9999;
	transform: translate(-50%, -50%);
	background-color: rgba(240, 242, 244, 0.85);
	cursor: move;
}

#settingsUIContainer::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	border-radius: inherit;
	backdrop-filter: blur(30px) saturate(180%);
	z-index: -1;
	border: 1px solid rgba(255, 255, 255, 0.8);
	box-shadow: 0 1px 50px rgba(0, 0, 0, 0.20), inset 0px 0px 60px 20px rgba(255, 255, 255, 0.6);
}

/* 确保交互元素鼠标样式正常 */
#settingsUIContainer input,
#settingsUIContainer textarea,
#settingsUIContainer button,
#settingsUIContainer label,
#settingsUIContainer .bgsh-setting-checkbox-label,
#settingsUIContainer .bgsh-checkbox-custom {
  cursor: auto;
}

#settingsUIContainer button {
  cursor: pointer;
}

#settingsUIContainer::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	border-radius: 12px;
	backdrop-filter: blur(30px) saturate(180%);
	z-index: -1;
	border: 1px solid rgba(255, 255, 255, 0.7);
	box-shadow: 0px 1px 50px rgba(0, 0, 0, 0.20), inset 0px 0px 30px rgba(255, 255, 255, 0.9);
}

/* 文本选择时的背景色 */
#settingsUIContainer *::selection {
  background-color: #c7c9ca;
}

/* 盒子 */
#settingsUIContainer .bgsh-setting-box {
  margin: auto;
  box-sizing: border-box;
  padding: 20px 20px;
  background-color: transparent;
  height: auto;
  display: flex;
  flex-direction: column;
}

/* 复选框标签样式 */
#settingsUIContainer .bgsh-setting-checkbox-label {
  display: block;
  position: relative;
  cursor: pointer;
  margin: 10px 0;
}

/* 复选框标签中的文本样式 */
#settingsUIContainer span {
  display: inline-block;
  position: absolute;
  line-height: 2.3;
  left: 8%;
}

/* 输入框样式 */
#settingsUIContainer input {
	padding-left: 10px;
	border: none;
	color: #858686;
	margin-top: 2px;
	background-color: rgb(131, 131, 135, 0.15);
	box-shadow: none;
  border-radius: 8px;
}

#settingsUIContainer textarea {
	width: 100%;
	height: 100px;
	padding-left: 10px;
	padding-top: 2px;
	border: none;
	color: #555;
	background-color: rgb(131, 131, 135, 0.15);
	margin: 5px 0;
	font-size: 12px;
}

/* 复选框输入框样式 */
#settingsUIContainer input[type="checkbox"] {
  position: absolute;
  opacity: 0;
}

/* 自定义复选框样式 */
#settingsUIContainer .bgsh-setting-checkbox-label .bgsh-setting-checkbox-custom {
  top: 8px;
  left: 0;
  height: 13px;
  width: 13px;
  border-radius: 50%;
  border: 1px solid #848484;
}

/* 复选框选中状态样式 */
#settingsUIContainer .bgsh-setting-checkbox-label input:checked ~ .bgsh-setting-checkbox-custom {
  background-color: #53C41B;
  border: none;
  transition: all 0.2s ease;
}

/* 按钮样式 */
#settingsUIContainer button {
  width: 50px;
  margin-top: 12px !important;
  height: 30px;
  border: none;
  outline: none;
  background-color: #52C51A;
  color: #fff;
  font-size: 13px;
  font-weight: 500  !important;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 1px 6px rgba(0, 0, 0, 0.1);
}

#settingsUIContainer button:hover {
  background-color: #41a411;
  text-shadow: 0 0px 5px rgba(255, 255, 255, 0.4);
}

#closeButton {
  background: #fc5b22 !important;
}

#closeButton:hover {
  background: red !important;
  text-shadow: 0 0px 5px rgba(255, 255, 255, 0.4);
}

/* 隐藏 <fieldset> 的边框 */
#settingsUIContainer fieldset {
  line-height: 2.0;
  margin: 4px 0 -10px 0;
  border: none;
}

/* 宽度 */
.bgsh-setting-first,
.bgsh-setting-second {
  width: 50%; /* 设定为50%宽度 */
}

/* 为了让 .bgsh-setting-first 和 .bgsh-setting-second 水平排列，我们需要一个外层的容器 */
.bgsh-setting-box-container {
  display: flex;
  gap: 20px;
}

.bgsh-setting-button {
  width: 100%;
  text-align: center;
  display: flex;
  gap: 20px;
  flex-direction: row;
  justify-content: center;
}

#blockedUsersList {
  height: 344px  !important;
}

#excludePostOptionsTextarea {
  height: 112px !important;
}
#maxGradeThread, #logoTextInput, #tipsTextInput {
	width: 210px;
	margin: 5px 0;
	height: 24px;
}
#settingsUIContainer > div > div.bgsh-setting-box-container > div.bgsh-setting-first > fieldset > label:nth-child(5) > input[type=radio] {
  background-color: transparent;
}

    `;

		const styleElement = document.createElement("style");
		styleElement.innerHTML = style;
		document.head.appendChild(styleElement);
	}

	// #endregion

	// #region 剪贴板操作

	/**
	 * 将指定文本复制到剪贴板。
	 * @param {string} text - 需要复制的文本。
	 * @param {function} onSuccess - 复制成功时的回调函数。
	 * @param {function} onError - 复制失败时的回调函数。
	 */
	async function copyToClipboard(text, onSuccess, onError) {
		try {
			await navigator.clipboard.writeText(text);
			if (onSuccess) {
				onSuccess();
			}
		} catch (err) {
			if (onError) {
				onError(err);
			}
			console.error("无法将文本复制到剪贴板", err);
		}
	}

	// #endregion

	// #region URL处理

	/**
	 * 从给定的URL中解析查询参数。
	 * @param {string} url - 需要解析的URL。
	 * @returns {object} 返回一个包含查询参数的对象。
	 */
	function getQueryParams(url) {
		const queryParams = {};

		// 检查 URL 是否包含传统的查询字符串
		if (url.includes("?")) {
			// 处理标准查询字符串
			const queryPattern = /[?&]([^=&]+)=([^&]*)/g;
			let match;
			while ((match = queryPattern.exec(url)) !== null) {
				queryParams[match[1]] = decodeURIComponent(match[2]);
			}
		} else {
			// 处理特殊的 URL 路径模式，如 forum-154-1.html
			const pathPattern = /forum-(\d+)-(\d+)\.html$/;
			const pathMatch = pathPattern.exec(url);
			if (pathMatch && pathMatch.length === 3) {
				// 假设第一个数字是 fid，第二个数字是 page
				queryParams.fid = pathMatch[1];
				queryParams.page = pathMatch[2];
			}
		}

		return queryParams;
	}

	/**
	 * 从给定的URL中解析tid。
	 * @param {string} url - 需要解析的URL。
	 * @returns {object} 返回一个包含查询参数的对象。
	 */
	function extractTid(url) {
		let tid = null;

		// 检查是否是类似 /thread-XXXX-1-1.html 的格式
		const threadMatch = url.match(/thread-(\d+)-\d+-\d+\.html/);
		if (threadMatch && threadMatch.length > 1) {
			tid = threadMatch[1];
		} else {
			// 检查是否是类似 /forum.php?mod=viewthread&tid=XXXX&extra=... 的格式
			const queryMatch = url.match(/tid=(\d+)/);
			if (queryMatch && queryMatch.length > 1) {
				tid = queryMatch[1];
			}
		}

		return tid;
	}

	// #endregion

	// #region DOM操作

	/**
	 * 获取指定名称的单选按钮的选中值。
	 * @param {string} name - 单选按钮的名称。
	 * @returns {number|null} 返回选中单选按钮的值，如果没有选中，则返回null。
	 */
	function getCheckedRadioValue(name) {
		const selectedRadio = document.querySelector(
			`input[name="${name}"]:checked`
		);
		return selectedRadio ? parseInt(selectedRadio.value) : 0;
	}

	/**
	 * 解析给定内容为DOM。
	 *
	 * @param {string} content - 要解析的内容。
	 * @param {string} type - 解析内容的类型，默认为"text/html"。
	 * @returns {Document} - 返回解析后的文档。
	 */
	function parseContent(content, type = "text/html") {
		return new DOMParser().parseFromString(content, type);
	}

	/**
	 * 从给定的DOM文档中提取值。
	 *
	 * @param {Document} doc - 要从中提取值的DOM文档。
	 * @param {string} selector - 用于定位元素的CSS选择器。
	 * @param {string} attr - 要提取的属性，默认为"value"。
	 * @returns {string|null} - 返回提取的值或null。
	 */
	function extractValueFromDOM(doc, selector, attr = "value") {
		const elem = doc.querySelector(selector);
		if (!elem) return null;
		return elem.getAttribute(attr);
	}

	/**
	 * 水印功能（已禁用）
	 */
	function showWatermarkMessage() {
		return; // 此功能已禁用
	}

	// #endregion

	// #region 用户信息获取

	/**
	 * 从页面中获取当前登录用户的 userid。
	 * @returns {string|null} 返回用户ID，如果未找到则返回null。
	 */
	function getUserId() {
		const userLink = document.querySelector(".vwmy a");
		if (userLink) {
			const match = userLink.href.match(/uid=(\d+)/);
			if (match) {
				return match[1];
			}
		}
		return null;
	}

	// #endregion

	// #region 帖子信息获取

	/**
	 * 从给定的元素中获取其父级table的帖子楼层Pid。
	 * @param {HTMLElement} element - 要查询的HTML元素。
	 * @returns {string|null} 返回楼层的Pid，如果未找到则返回null。
	 */
	function getTableIdFromElement(element) {
		if (element) {
			let parentTable = element.closest("table");
			if (parentTable && parentTable.id.startsWith("pid")) {
				return parentTable.id.replace("pid", "");
			}
		}
		return null;
	}

	/**
	 * 检测帖子是否为置顶贴
	 * @param {HTMLElement} element - 帖子元素
	 * @returns {boolean} 如果是置顶贴返回true，否则返回false
	 */
	function isStickyThread(element) {
		// 检查新布局中的置顶贴
		// 1. 检查元素本身是否为置顶卡片
		if (element.classList && element.classList.contains('thread-card') && element.classList.contains('sticky')) {
			return true;
		}

		// 2. 检查祖先元素中是否包含sticky class
		const threadCard = element.closest(".thread-card.sticky");
		if (threadCard) {
			return true;
		}

		// 3. 检查原始布局中的置顶贴 - tbody id包含stickthread_
		const tbody = element.closest('tbody');
		if (tbody && tbody.id && tbody.id.includes('stickthread_')) {
			return true;
		}

		return false;
	}

	// #endregion

	// #region 会话验证

	/**
	 * 从页面中获取formhash值。
	 * @returns {string|null} 返回formhash值，如果未找到则返回null。
	 */
	function getFormHash() {
		const element = document.querySelector('input[name="formhash"]');
		if (element) {
			return element.value;
		} else {
			return null;
		}
	}

	// #endregion

	// #region 页码操作

	/**
	 * 从页面的一个位置复制页码到另一个位置。
	 */
	function addPageNumbers() {
		const sourceElement = document.querySelector(".pgs.cl.mbm");
		const targetElement = document.querySelector(".slst.mtw");

		if (!sourceElement) {
			console.error("源元素（使用选择器 '.pgs.cl.mbm'）未找到！");
			return;
		}

		if (!targetElement) {
			console.error("目标元素（使用选择器 '.slst.mtw'）未找到！");
			return;
		}

		const parentElement = targetElement.parentElement;

		if (!parentElement) {
			console.error("目标元素的父元素不可用！");
			return;
		}

		const clonedElement = sourceElement.cloneNode(true);
		parentElement.insertBefore(clonedElement, targetElement);
	}

	// #endregion

	// #region UI组件创建

	/**
	 * 创建一个按钮并添加到指定的容器中。
	 *
	 * @param {string} id 按钮的ID
	 * @param {string} text 按钮上显示的文本
	 * @param {Function} clickHandler 按钮被点击时触发的函数
	 * @returns {HTMLButtonElement} 创建的按钮元素
	 */
	const createButton = (
		id,
		text,
		clickFunction,
		className = "bgsh-customBtn",
		bgColor = "#0396FF"
	) => {
		const button = document.createElement("button");
		button.id = id;
		button.innerText = text;
		button.className = className;
		button.style.backgroundColor = bgColor; // 设置背景颜色
		button.addEventListener("click", clickFunction);
		return button;
	};

	/**
	 * 创建一个固定位置的按钮容器。
	 * @returns {HTMLElement} 返回创建的按钮容器元素。
	 */
	function createButtonContainer() {
		const container = document.createElement("div");
		Object.assign(container.style, {
			display: "flex",
			flexDirection: "column",
			alignItems: "center",
			position: "fixed",
			top: "50%",
			right: "1vh",
			zIndex: "1000",
			transform: "translateY(-50%)",
		});

		return container;
	}

	// #endregion

	// #region 用户签到功能

	/**
	 * 为给定的用户执行签到操作。
	 *
	 * @param {string} userid 用户ID
	 * @returns {boolean} 签到成功返回 true，否则返回 false
	 */
	async function sign(userid) {
		const signURL = `${baseURL}/plugin.php?id=dd_sign&ac=sign&infloat=yes&handlekey=pc_click_ddsign&inajax=1&ajaxtarget=fwin_content_pc_click_ddsign`;
		const params = await getSignParameters(signURL);

		if (!params || !params.formhash || !params.signhash) {
			console.error("Failed to retrieve sign parameters.");
			return false;
		}

		const { formhash, signtoken, signhash } = params;
		const secanswer = await getValidationResult();

		let responseText = await postSignData({
			baseURL,
			formhash,
			signtoken,
			secanswer,
			signhash,
		});
		return updateSignButton(responseText, userid);
	}

	/**
	 * 从指定的 URL 获取签到所需的参数。
	 *
	 * @param {string} url 目标URL
	 * @returns {Object|null} 包含签到参数的对象或null
	 */
	async function getSignParameters(url) {
		const { responseText, contentType } = await fetchWithContentType(url);
		return handleResponseContent(responseText, contentType);
	}

	async function getValidationResult() {
		const secqaaURL = `/misc.php?mod=secqaa&action=update&idhash=qSAxcb0`;
		const { responseText, contentType } = await fetchWithContentType(secqaaURL);
		return extractValidationText(responseText, contentType);
	}

	async function postSignData({
		baseURL,
		formhash,
		signtoken,
		secanswer,
		signhash,
	}) {
		const postURL = `${baseURL}/plugin.php?id=dd_sign&ac=sign&signsubmit=yes&handlekey=pc_click_ddsign&signhash=${signhash}&inajax=1`;
		const data = new URLSearchParams({
			formhash,
			signtoken,
			secanswer,
			secqaahash: "qSAxcb0",
		});
		const response = await fetch(postURL, {
			method: "post",
			headers: {
				"Content-Type": "application/x-www-form-urlencoded",
			},
			body: data,
		});
		return response.text();
	}

	async function fetchWithContentType(url) {
		const response = await fetch(url);
		const contentType = response.headers.get("Content-Type");
		const responseText = await response.text();
		return { responseText, contentType };
	}

	function handleResponseContent(responseText, contentType) {
		if (contentType.includes("text/xml")) {
			return handleXMLContent(responseText);
		} else if (contentType.includes("text/html")) {
			return extractSignParametersFromHTML(responseText);
		} else {
			throw new Error("Unsupported content type");
		}
	}

	/**
	 * 处理XML内容并提取所需的签到参数。
	 *
	 * @param {string} responseText - 从请求中返回的XML内容。
	 * @returns {object|null} - 返回提取的签到参数或null。
	 */
	function handleXMLContent(responseText) {
		let xml = parseContent(responseText, "text/xml");
		let content = xml.getElementsByTagName("root")[0].textContent;
		let doc = parseContent(content);
		const alertErrorElement = doc.querySelector(".alert_error");
		if (alertErrorElement) {
			let scripts = alertErrorElement.querySelectorAll("script");
			scripts.forEach((script) => {
				script.remove();
			});
			showTooltip(alertErrorElement.textContent.trim());
			return;
		} else {
			return extractSignParametersFromHTML(content);
		}
	}

	/**
	 * 从HTML内容中提取签到参数。
	 *
	 * @param {string} responseText - 从请求中返回的HTML内容。
	 * @returns {object} - 返回提取的签到参数。
	 */
	function extractSignParametersFromHTML(responseText) {
		const doc = parseContent(responseText);
		const formhash = extractValueFromDOM(doc, 'input[name="formhash"]');
		const signtoken = extractValueFromDOM(doc, 'input[name="signtoken"]');
		const signhash = extractValueFromDOM(
			doc,
			'form[name="login"]',
			"id"
		).replace("signform_", "");
		return { formhash, signtoken, signhash };
	}

	/**
	 * 从验证结果文本中提取计算表达式并计算结果。
	 *
	 * @param {string} resultText 验证结果文本
	 * @param {string} contentType 内容类型
	 * @returns {number} 计算的结果
	 */
	function extractValidationText(resultText, contentType) {
		const text = resultText
			.replace("sectplcode[2] + '", "前")
			.replace("' + sectplcode[3]", "后");
		const matchedText = text.match(/前([\w\W]+)后/)[1];
		return computeExpression(matchedText.replace("= ?", ""));
	}

	/**
	 * 计算给定的数学表达式。
	 *
	 * @param {string} expr 数学表达式
	 * @returns {number} 计算的结果
	 */
	const computeExpression = (expr) => {
		const [left, operator, right] = expr.split(/([+\-*/])/);
		const a = parseFloat(left.trim());
		const b = parseFloat(right.trim());
		switch (operator) {
			case "+":
				return a + b;
			case "-":
				return a - b;
			case "*":
				return a * b;
			case "/":
				return a / b;
			default:
				throw new Error("Unsupported operator");
		}
	};

	/**
	 * 根据签到响应内容更新签到按钮的状态，并设置最后签到日期。
	 *
	 * @param {string} responseText 签到响应内容
	 * @param {string} userid 用户ID
	 * @returns {boolean} 签到成功返回 true，否则返回 false
	 */
	function updateSignButton(responseText, userid) {
		const today = new Date().toLocaleDateString();
		if (
			responseText.includes("已经签到过") ||
			responseText.includes("重复签到")
		) {
			showTooltip("已经签到过啦，请明天再来！");
			GM_setValue(`lastSignDate_${userid}`, today);
			return true;
		} else if (responseText.includes("签到成功")) {
			showTooltip("签到成功，金钱+2，明天记得来哦。");
			GM_setValue(`lastSignDate_${userid}`, today);
			return true;
		} else if (responseText.includes("请至少发表或回复一个帖子后再来签到")) {
			showTooltip("请至少发表或回复一个帖子后再来签到!");
			return false;
		} else {
			showTooltip("抱歉，签到出现了未知错误！");
			return false;
		}
	}

	// #endregion

	// #region 收藏帖子操作

	/**
	 * 收藏当前查看的帖子。
	 * 通过构造特定URL实现收藏功能，同时给用户提供收藏成功或失败的提示。
	 * @async
	 * @function
	 */
	async function star() {
		// 移除水印效果调用
		// showWatermarkMessage();
		// 从当前页面URL中获取帖子的tid
		const tid = extractTid(window.location.href);
		// 获取formhash，用于验证请求
		const formHash = document.querySelector('input[name="formhash"]').value;

		// 构造收藏URL
		const starUrl = `/home.php?mod=spacecp&ac=favorite&type=thread&id=${tid}&formhash=${formHash}&infloat=yes&handlekey=k_favorite&inajax=1&ajaxtarget=fwin_content_k_favorite`;

		// 发送收藏请求
		const text = await fetch(starUrl).then((r) => r.text());

		// 根据响应内容提供相应的提示
		if (text.includes("抱歉，您已收藏，请勿重复收藏")) {
			return showTooltip("抱歉，您已收藏，请勿重复收藏");
		}

		if (text.includes("信息收藏成功")) {
			return showTooltip("信息收藏成功");
		}

		// 如果既没有成功消息也没有重复收藏消息，视为出错并在控制台记录
		showTooltip("信息收藏出现问题！！！");
		console.error(text);
	}

	// #endregion

	// #region 帖子评分

	/**
	 * 获取指定帖子的评分信息。
	 * @param {number} pid - 帖子ID。
	 * @param {number} tid - 主题ID。
	 * @param {number} timestamp - 当前时间戳。
	 * @returns {Object} 评分信息。
	 * @async
	 */
	async function getRateInfo(pid, tid, timestamp) {
		const infoDefaults = {
			state: false,
			max: 0,
			left: 0,
			formHash: "",
			referer: "",
			handleKey: "",
			error: "",
		};

		try {
			// 构建评分信息请求的URL
			const url = `/forum.php?mod=misc&action=rate&tid=${tid}&pid=${pid}&infloat=yes&handlekey=rate&t=${timestamp}&inajax=1&ajaxtarget=fwin_content_rate`;
			const response = await fetch(url);
			if (!response.ok) throw new Error("Failed to fetch rate info");

			// 解析服务器返回的XML数据
			const text = await response.text();
			const xml = new DOMParser().parseFromString(text, "text/xml");
			const htmlContent = xml.querySelector("root").textContent;
			const doc = new DOMParser().parseFromString(htmlContent, "text/html");

			// 检查是否存在错误
			if (htmlContent.includes("alert_error")) {
				const alertErrorElement = doc.querySelector(".alert_error");
				const scriptElements = alertErrorElement.querySelectorAll("script");
				scriptElements.forEach((script) => script.remove());

				const errorMessage = alertErrorElement.textContent.trim();
				return { ...infoDefaults, error: errorMessage };
			}

			// 提取评分信息
			const maxElement = doc.querySelector("#scoreoption8 li");
			if (!maxElement) {
				return { ...infoDefaults, error: "评分不足啦!" };
			}

			const max = parseInt(maxElement.textContent.replace("+", ""), 10);
			const left = parseInt(
				doc.querySelector(".dt.mbm td:last-child").textContent,
				10
			);
			const formHash = doc.querySelector('input[name="formhash"]').value;
			const referer = doc.querySelector('input[name="referer"]').value;
			const handleKey = doc.querySelector('input[name="handlekey"]').value;

			return {
				state: true,
				max: Math.min(max, left),
				left,
				formHash,
				referer,
				handleKey,
				error: "",
			};
		} catch (error) {
			showTooltip(error);
			return infoDefaults;
		}
	}

	/**
	 * 对指定的帖子进行评分。
	 * @param {number} pid - 帖子ID。
	 * @async
	 */
	async function grade(pid) {
		// 移除水印效果调用
		// showWatermarkMessage();
		const tid = extractTid(window.location.href);
		const timestamp = new Date().getTime();
		const rateInfo = await getRateInfo(pid, tid, timestamp);
		if (!rateInfo.state) {
			showTooltip(rateInfo.error);
			return;
		}
		var settings = getSettings();
		var maxGradeThread = settings.maxGradeThread;
		rateInfo.max =
			parseInt(rateInfo.max) < parseInt(maxGradeThread)
				? rateInfo.max
				: maxGradeThread;
		// 构建评分请求的URL和数据
		const rateUrl =
			"/forum.php?mod=misc&action=rate&ratesubmit=yes&infloat=yes&inajax=1";
		const data = new URLSearchParams();
		data.append("formhash", rateInfo.formHash);
		data.append("tid", tid);
		data.append("pid", pid);
		data.append("referer", rateInfo.referer);
		data.append("handlekey", rateInfo.handleKey);
		data.append("score8", `+${rateInfo.max}`);
		data.append("reason", settings.logoText);
		data.append("sendreasonpm", "on");

		// 发送评分请求
		const request = new Request(rateUrl, {
			method: "post",
			headers: {
				"Content-Type": "application/x-www-form-urlencoded",
			},
			body: data,
		});

		try {
			const responseText = await fetch(request).then((r) => r.text());

			// 根据响应内容提供评分成功或失败的提示
			if (responseText.includes("感谢您的参与，现在将转入评分前页面")) {
				showTooltip(`+${rateInfo.max} 评分成功，并通知了楼主!`);
			} else {
				showTooltip("抱歉，评分失败！");
				console.error(responseText);
			}
		} catch (error) {
			showTooltip("评分请求失败！");
			console.error(error);
		}
	}

	// #endregion

	// #region 帖子置顶

	/**
	 * 获取指定帖子的置顶信息。
	 * @param {number} fid - 板块ID。
	 * @param {number} tid - 主题ID。
	 * @param {number} pid - 楼层ID。
	 * @returns {Object} 置顶信息。
	 * @async
	 */
	async function getTopicadmin(fid, tid, pid) {
		const infoDefaults = {
			state: false,
			action: "",
			formhash: "",
			page: "",
			handlekey: "",
			error: "",
		};
		try {
			const formhash = getFormHash();

			// 构建评分信息请求的URL
			const url = `/forum.php?mod=topicadmin&action=stickreply&fid=${fid}&tid=${tid}&handlekey=mods&infloat=yes&nopost=yes&inajax=1`;
			const data = new URLSearchParams();
			data.append("formhash", formhash);
			data.append("optgroup", "");
			data.append("operation", "");
			data.append("listextra", "");
			data.append("page", 1);
			data.append("topiclist[]", pid);

			const request = new Request(url, {
				method: "post",
				headers: {
					"Content-Type": "application/x-www-form-urlencoded",
				},
				body: data,
			});

			const text = await fetch(request).then((r) => r.text());

			const xml = new DOMParser().parseFromString(text, "text/xml");
			const htmlContent = xml.querySelector("root").textContent;
			const doc = new DOMParser().parseFromString(htmlContent, "text/html");

			// 检查是否存在错误
			if (htmlContent.includes("alert_error")) {
				const alertErrorElement = doc.querySelector(".alert_error");
				const scriptElements = alertErrorElement.querySelectorAll("script");
				scriptElements.forEach((script) => script.remove());

				const errorMessage = alertErrorElement.textContent.trim();
				return { ...infoDefaults, error: errorMessage };
			}

			// 提取评分信息
			const element = doc.querySelector("#topicadminform");
			if (!element) {
				return { ...infoDefaults, error: "提取置顶信息失败拉!" };
			}
			// 获取元素的action属性值
			const action =
				element.getAttribute("action").replace(/amp;/g, "") + "&inajax=1";
			const newformhash = element.querySelector('input[name="formhash"]').value;
			const page = element.querySelector('input[name="page"]').value;
			const handlekey = element.querySelector('input[name="handlekey"]').value;

			return {
				state: true,
				action,
				formhash: newformhash,
				page,
				handlekey,
				error: "",
			};
		} catch (error) {
			showTooltip(error);
			return infoDefaults;
		}
	}

	/**
	 * 对指定的帖子进行置顶。
	 * @param {number} pid - 帖子ID。
	 * @async
	 */
	async function topicadmin(pid, stickreply) {
		// 移除水印效果调用
		// showWatermarkMessage();
		const tid = extractTid(window.location.href);
		let fid = getFidFromElement();

		if (!fid) {
			showTooltip("获取板块ID失败！");
			return;
		}
		const topicadminInfo = await getTopicadmin(fid, tid, pid);
		if (!topicadminInfo.state) {
			showTooltip(topicadminInfo.error);
			return;
		}
		const settings = getSettings();
		// 构建置顶请求的URL和数据
		const topicadminUrl = `/${topicadminInfo.action}`;
		const data = new URLSearchParams();
		data.append("formhash", topicadminInfo.formhash);
		data.append("fid", fid);
		data.append("tid", tid);
		data.append("topiclist[]", pid);
		data.append("page", topicadminInfo.page);
		data.append("handlekey", topicadminInfo.handlekey);
		data.append("stickreply", stickreply);
		data.append("reason", settings.logoText);
		data.append("sendreasonpm", "on");

		// 发送置顶请求
		const request = new Request(topicadminUrl, {
			method: "post",
			headers: {
				"Content-Type": "application/x-www-form-urlencoded",
			},
			body: data,
		});

		try {
			const responseText = await fetch(request).then((r) => r.text());

			// 根据响应内容提供置顶成功或失败的提示
			if (responseText.includes("操作成功 ")) {
				showTooltip(
					stickreply == "1"
						? ` 置顶成功，并通知了楼主!`
						: ` 取消置顶成功，并通知了楼主!`
				);
			} else {
				showTooltip("抱歉，置顶失败！");
				console.error(responseText);
			}
		} catch (error) {
			showTooltip("置顶请求失败！");
			console.error(error);
		}
	}

	/**
	 * 从指定的元素中提取fid值
	 *
	 * @param {string} elementId - 要查找的元素的ID。
	 * @returns {string|null} 返回fid值，如果找不到则返回null。
	 */
	function getFidFromElement() {
		// 通过ID查找页面上的元素
		let element = document.querySelector("#newspecial");
		// 如果元素不存在，返回null
		if (!element) return null;

		// 获取元素的onclick属性值
		let hrefValue = element.getAttribute("onclick");

		// 如果onclick属性不存在，返回null
		if (!hrefValue) return null;

		// 使用正则表达式匹配fid的值
		let match = /fid=(\d+)/.exec(hrefValue);

		// 如果匹配成功，返回fid的值，否则返回null
		return match ? match[1] : null;
	}

	// #endregion

	// #region 一键评分与收藏

	/**
	 * 对首帖进行评分并收藏该帖子。
	 * 1. 从页面中选择首帖元素。
	 * 2. 从该元素获取帖子ID。
	 * 3. 对帖子进行评分。
	 * 4. 收藏该帖子。
	 */
	function gradeAndStar() {
		// 获取首帖元素
		let firstPobClElement = document.querySelector(".po.hin");
		// 从首帖元素中提取帖子ID
		let pid = getTableIdFromElement(firstPobClElement);

		// 对首帖进行评分
		grade(pid);
		// 收藏首帖
		star();
	}

	// #endregion

	// #region 勋章操作

	/**
	 * 判断是否应用设置
	 * @param {number} setting - 设置值
	 * @param {boolean} targetMatch - 目标匹配标志
	 * @return {boolean} - 是否应用设置
	 */
	function shouldApplySetting(setting, targetMatch) {
		return setting === 1 || (setting === 2 && targetMatch);
	}

	/**
	 * 隐藏勋章
	 * @param {HTMLImageElement} img - 勋章图片
	 * @param {boolean} targetMatch - 目标匹配标志
	 * @param {number} setting - 设置值
	 */
	function hideMedal(img, targetMatch, setting) {
		if (shouldApplySetting(setting, targetMatch)) {
			img.style.display = "none";
		} else {
			img.style.display = "";
		}
	}

	/**
	 * 调整勋章大小
	 * @param {HTMLImageElement} img - 勋章图片
	 * @param {boolean} targetMatch - 目标匹配标志
	 * @param {number} setting - 设置值
	 * @param {string} size - 新的图片大小
	 */
	function resizeMedal(img, targetMatch, setting, size) {
		if (shouldApplySetting(setting, targetMatch)) {
			img.style.width = size;
		} else {
			img.style.width = "auto";
		}
	}

	/**
	 * 替换勋章
	 * @param {HTMLImageElement} img - 勋章图片
	 * @param {boolean} targetMatch - 目标匹配标志
	 * @param {number} setting - 设置值
	 * @param {string} newUrl - 新的图片URL
	 */
	function replaceMedal(img, targetMatch, setting, newUrl) {
		if (shouldApplySetting(setting, targetMatch)) {
			img.src = newUrl;
			img.style.width = "50px";
		}
	}

	/**
	 * 主要的勋章操作函数
	 * @param {object} settings - 设置对象
	 */
	function manipulateMedals(settings) {
		const excludeNumbers = [
			17, 29, 31, 32, 33, 34, 35, 36, 37, 38, 110, 111, 112, 113, 114, 116, 117,
		];
		const targetMedalNumbers = Array.from({ length: 122 }, (_, i) => i + 14)
			.filter((num) => !excludeNumbers.includes(num))
			.map((num) => `medal${num}`);

		document.querySelectorAll(".md_ctrl img").forEach((img) => {
			const imgSrc = img.src;
			const targetMatch = targetMedalNumbers.some((target) =>
				imgSrc.includes(target)
			);

			hideMedal(img, targetMatch, settings.blockMedals);
		});
	}

	// #endregion

	// #region 用户内容屏蔽

	/**
	 * 检测是否存在discuz_redesign脚本的新布局
	 */
	function isRedesignedLayout() {
		const redesignedList = document.querySelector('.redesigned-thread-list');
		return redesignedList !== null;
	}

	/**
	 * 强制隐藏卡片元素，确保与discuz_redesign.js的CSS兼容
	 * @param {HTMLElement} card - 要隐藏的卡片元素
	 */
	function forceHideCard(card) {
		// 添加一个特殊的class来标记被屏蔽的卡片
		card.classList.add('blocked-card');

		// 使用多种方式确保隐藏效果
		card.style.setProperty('display', 'none', 'important');
		card.style.setProperty('visibility', 'hidden', 'important');
		card.style.setProperty('height', '0', 'important');
		card.style.setProperty('overflow', 'hidden', 'important');
		card.style.setProperty('margin', '0', 'important');
		card.style.setProperty('padding', '0', 'important');
		card.style.setProperty('border', 'none', 'important');
		card.style.setProperty('opacity', '0', 'important');

		// 从DOM流中移除
		card.setAttribute('aria-hidden', 'true');
	}

	/**
	 * 在新的卡片布局中屏蔽用户内容
	 * @param {object} settings - 设置对象，包含被屏蔽的用户列表和显示消息选项
	 */
	function blockContentInRedesignedLayout(settings) {
		const { blockedUsers, displayBlockedTips } = settings;
		const threadCards = document.querySelectorAll('.thread-card');

		threadCards.forEach((card, index) => {
			const authorElement = card.querySelector('.thread-author');
			if (authorElement) {
				const authorName = authorElement.textContent.trim();

				if (blockedUsers.includes(authorName)) {
					if (displayBlockedTips) {
						// 替换卡片内容为屏蔽提示
						card.innerHTML = `
							<div class="thread-content" style="padding: 20px; text-align: center; color: #666;">
								<div class="thread-title">
									<b>已屏蔽主题</b>
								</div>
								<div style="margin-top: 8px; font-size: 12px;">
									用户: <span style="color: #999;">${authorName}</span>
								</div>
							</div>
						`;
						card.style.background = 'rgba(200, 200, 200, 0.3)';
					} else {
						// 使用强制隐藏函数
						forceHideCard(card);
					}
				}
			}
		});
	}

	/**
	 * 根据设置屏蔽指定用户的内容
	 * 注意：原始布局的用户屏蔽逻辑已移除，现在只支持新的卡片布局
	 * @param {object} settings - 设置对象，包含被屏蔽的用户列表和显示消息选项
	 */
	function blockContentByUsers(settings) {
		// 检测是否使用了新的布局
		const isNewLayout = isRedesignedLayout();

		if (isNewLayout) {
			blockContentInRedesignedLayout(settings);
			return;
		}

		// 原始布局的用户屏蔽功能已移除，因为discuz_redesign.js已经改变了页面结构
	}

	/**
	 * 在新的卡片布局中根据关键词屏蔽内容
	 * @param {object} settings - 设置对象，包含被屏蔽的关键词列表和显示消息选项
	 */
	function blockContentByTitleInRedesignedLayout(settings) {
		const { excludePostOptions, displayBlockedTips } = settings;

		// 如果没有关键字，直接返回
		if (!excludePostOptions || excludePostOptions.length === 0) {
			return;
		}

		const threadCards = document.querySelectorAll('.thread-card:not([data-keyword-checked])');

		threadCards.forEach((card, index) => {
			// 标记为已检查，避免重复处理
			card.setAttribute('data-keyword-checked', 'true');

			const titleElement = card.querySelector('.thread-title a');
			if (titleElement) {
				// 尝试多种方式获取完整标题
				let title = titleElement.textContent.trim();

				// 如果标题太短，尝试获取innerHTML并清理HTML标签
				if (title.length < 10) {
					const innerHTML = titleElement.innerHTML;
					title = innerHTML.replace(/<[^>]*>/g, '').trim();
				}

				// 如果还是太短，尝试从父元素获取
				if (title.length < 10) {
					const parentTitle = card.querySelector('.thread-title');
					if (parentTitle) {
						title = parentTitle.textContent.trim();
					}
				}

				// 详细检查每个关键字
				let matchedKeyword = null;

				for (let i = 0; i < excludePostOptions.length; i++) {
					const keyword = excludePostOptions[i];

					if (title.includes(keyword)) {
						matchedKeyword = keyword;
						break;
					}
				}

				if (matchedKeyword) {
					if (displayBlockedTips) {
						// 替换卡片内容为屏蔽提示
						card.innerHTML = `
							<div class="thread-content" style="padding: 20px; text-align: center; color: #666;">
								<div class="thread-title">
									<b>已屏蔽主题关键词: ${matchedKeyword}</b>
								</div>
								<div style="margin-top: 8px; font-size: 12px; color: #999;">
									原标题包含屏蔽关键词
								</div>
							</div>
						`;
						card.style.background = 'rgba(200, 200, 200, 0.3)';
					} else {
						// 使用强制隐藏函数
						forceHideCard(card);
					}
				}
			}
		});
	}

	/**
	 * 改进的关键字匹配函数
	 * @param {string} text - 要检查的文本
	 * @param {Array} keywords - 关键字列表
	 * @returns {string|null} - 匹配的关键字，如果没有匹配则返回null
	 */
	function findMatchingKeyword(text, keywords) {
		if (!text || !keywords || keywords.length === 0) {
			return null;
		}

		const lowerText = text.toLowerCase();

		for (const keyword of keywords) {
			if (!keyword || keyword.trim() === '') continue;

			const lowerKeyword = keyword.toLowerCase().trim();

			// 检查关键词是否包含中文字符
			const containsChinese = /[\u4e00-\u9fa5]/.test(lowerKeyword);

			if (containsChinese) {
				// 对于中文：直接包含匹配
				if (lowerText.includes(lowerKeyword)) {
					return keyword;
				}
			} else {
				// 对于英文：使用词边界匹配，避免部分匹配
				const regex = new RegExp('\\b' + escapeRegExp(lowerKeyword) + '\\b', 'i');
				if (regex.test(lowerText)) {
					return keyword;
				}

				// 如果词边界匹配失败，也尝试直接包含匹配（兼容性）
				if (lowerText.includes(lowerKeyword)) {
					return keyword;
				}
			}
		}

		return null;
	}

	/**
	 * 转义正则表达式特殊字符
	 * @param {string} string - 要转义的字符串
	 * @returns {string} - 转义后的字符串
	 */
	function escapeRegExp(string) {
		return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
	}

	/**
	 * 根据设置的关键词屏蔽帖子标题
	 * 注意：原始布局的关键字屏蔽逻辑已移除，现在只支持新的卡片布局
	 * @param {object} settings - 设置对象，包含被屏蔽的用户列表和显示消息选项
	 */
	function blockContentByTitle(settings) {
		// 检测是否使用了新的布局
		const isNewLayout = isRedesignedLayout();

		if (isNewLayout) {
			blockContentByTitleInRedesignedLayout(settings);
			return;
		}

		// 原始布局的关键字屏蔽功能已移除，因为discuz_redesign.js已经改变了页面结构
	}

	// 注意：createBlockAction 和 applyBlockAction 函数已移除
	// 这些函数只用于原始布局的XPath屏蔽逻辑，现在已不再需要

	// #endregion

	// #region 无缝翻页

	/**
	 * 初始化无限滚动功能
	 * 根据用户的滚动行为来加载下一页的内容。
	 *
	 * @param {string} pageName 页面名称，用于确定要加载哪种内容
	 */
	function initInfiniteScroll(pageName) {
		// 检查是否已经初始化过，避免重复初始化
		if (window.infiniteScrollInitialized) {
			console.log('无限滚动已经初始化，跳过重复初始化');
			return;
		}

		let isLoading = false;
		let noMoreData = false;
		const settings = getSettings();

		// 根据传入的页面名称决定内容选择器
		let contentSelector;
		let alternativeSelectors = [];

		switch (pageName) {
			case "isSearchPage":
				contentSelector = "#threadlist";
				// 搜索页面的备选选择器
				alternativeSelectors = [
					"#searchlist",
					".pbw",
					".tl",
					"#ct .bm_c",
					".ct2 .bm_c",
					"#main .bm_c",
					"#ct",
					".ct2",
					"#main",
					".bm_c",
					"tbody",
					"table",
					".t",
					".forum",
					"#content",
					".content",
					"#wrapper",
					".wrapper"
				];
				break;
			case "isForumDisplayPage":
				contentSelector = "#threadlist";
				break;
			case "isPostPage":
				contentSelector = "#postlist";
				break;
			case "isSpacePage":
				contentSelector = "#delform";
				break;
			default:
				contentSelector = "#threadlist"; // 默认选择器
		}

		if (!settings.autoPagination) {
			return;
		}

		// 标记已初始化
		window.infiniteScrollInitialized = true;

		/**
		 * 加载下一个页面的内容。
		 * 获取当前页面中的"下一页"链接，然后抓取该链接的内容，
		 * 并将新内容添加到当前页面。
		 */
		async function loadNextPage() {
			const nextPageLink = document.querySelector(".nxt");
			if (!nextPageLink || noMoreData) {
				if (!noMoreData) {
					showTooltip("已经是全部数据了");
					noMoreData = true;
				}
				return;
			}

			// 检查请求间隔，避免触发网站频率限制
			const now = Date.now();
			const timeSinceLastRequest = now - (window.lastRequestTime || 0);
			const minInterval = 2000; // 最小间隔2秒

			if (timeSinceLastRequest < minInterval) {
				const waitTime = minInterval - timeSinceLastRequest;
				setTimeout(() => {
					loadNextPage();
				}, waitTime);
				return;
			}

			isLoading = true;
			window.lastRequestTime = now;
			const url = nextPageLink.getAttribute("href");

			try {
				const response = await fetch(url);

				// 检查响应状态
				if (!response.ok) {
					throw new Error(`HTTP ${response.status}: ${response.statusText}`);
				}

				const text = await response.text();

				// 检查是否包含频率限制提示
				if (text.includes('刷新过于频繁') || text.includes('请3秒后再试') || text.includes('访问过于频繁')) {
					console.log('检测到频率限制，等待5秒后重试');
					showTooltip('访问过于频繁，5秒后自动重试');
					isLoading = false;
					setTimeout(() => {
						loadNextPage();
					}, 5000);
					return;
				}

				const div = document.createElement("div");
				div.innerHTML = text;

			const newNextPageLink = div.querySelector(".nxt");
			newNextPageLink
				? nextPageLink.setAttribute(
						"href",
						newNextPageLink.getAttribute("href")
				  )
				: (noMoreData = true);

			// 尝试找到新内容容器
			let newContent = div.querySelector(contentSelector);

			// 如果主选择器找不到，尝试备选选择器
			if (!newContent && alternativeSelectors.length > 0) {
				console.log('主选择器未找到内容，尝试备选选择器:', contentSelector);
				for (const altSelector of alternativeSelectors) {
					newContent = div.querySelector(altSelector);
					if (newContent) {
						console.log('使用备选选择器找到内容:', altSelector);
						// 更新当前使用的选择器
						contentSelector = altSelector;
						break;
					}
				}
			}

			// 如果还是找不到，对于搜索页面尝试智能检测
			if (!newContent && pageName === "isSearchPage") {
				console.log('搜索页面智能检测模式');
				// 尝试找到包含搜索结果的任何容器
				const possibleContainers = div.querySelectorAll('div, table, tbody, ul, ol');
				for (const container of possibleContainers) {
					// 检查容器是否包含搜索结果相关的内容
					if (container.innerHTML && (
						container.innerHTML.includes('thread-') ||
						container.innerHTML.includes('normalthread') ||
						container.innerHTML.includes('stickthread') ||
						container.innerHTML.includes('.htm') ||
						container.innerHTML.includes('forum.php') ||
						container.querySelector('a[href*="thread-"]') ||
						container.querySelector('a[href*="forum.php"]')
					)) {
						newContent = container;
						console.log('智能检测找到搜索结果容器:', container.tagName, container.className || container.id);
						break;
					}
				}
			}

			if (!newContent) {
				console.error('无法在新页面中找到内容容器，已尝试的选择器:', [contentSelector, ...alternativeSelectors]);
				console.log('新页面HTML片段:', div.innerHTML.substring(0, 1500));

				// 输出页面中所有可能的容器元素，帮助调试
				const allContainers = div.querySelectorAll('div[id], div[class], table[id], table[class], tbody, ul[id], ul[class]');
				console.log('页面中的所有容器元素:', Array.from(allContainers).map(el => ({
					tag: el.tagName,
					id: el.id,
					class: el.className,
					hasLinks: el.querySelectorAll('a').length > 0
				})));

				isLoading = false;
				return;
			}

			appendNewContent(newContent);

			const newPagination = div.querySelector(".pg");
			if (newPagination) {
				updatePagination(newPagination);
			} else {
				console.warn('无法在新页面中找到分页元素');
			}

			const newSettings = getSettings();
			await processPageContentBasedOnSettings(pageName, newSettings);
			blockContentByUsers(settings);

				// 对新加载的内容也执行关键字屏蔽
				setTimeout(() => {
					blockContentByTitleInRedesignedLayout(settings);
				}, 200);

				isLoading = false;
				checkAndLoadIfContentNotEnough();
			} catch (error) {
				console.error('加载下一页时发生错误:', error);
				showTooltip('加载失败，请稍后重试');
				isLoading = false;

				// 如果是网络错误，等待一段时间后重试
				if (error.message.includes('Failed to fetch') || error.message.includes('Network')) {
					setTimeout(() => {
						console.log('网络错误，5秒后重试');
						loadNextPage();
					}, 5000);
				}
			}
		}

		/**
		 * 将新页面中的内容添加到当前页面。
		 * @param {Element} newContent 新的内容元素
		 */
		function appendNewContent(newContent) {
			// 检查新内容是否存在
			if (!newContent) {
				console.error('appendNewContent: newContent 为 null');
				return;
			}

			// 尝试找到当前页面的内容容器
			let currentContent = document.querySelector(contentSelector);

			// 如果找不到，尝试备选选择器
			if (!currentContent && alternativeSelectors.length > 0) {
				for (const altSelector of alternativeSelectors) {
					currentContent = document.querySelector(altSelector);
					if (currentContent) {
						console.log('当前页面使用备选选择器:', altSelector);
						break;
					}
				}
			}

			if (!currentContent) {
				console.error('找不到当前页面的内容容器:', contentSelector);
				return;
			}

			// 检查是否存在新的卡片布局
			const redesignedContainer = document.querySelector('.redesigned-thread-list');

			if (redesignedContainer && (contentSelector === "#threadlist" || contentSelector.includes("threadlist"))) {
				// 如果存在新布局且当前是帖子列表页，需要同时更新两个容器

				// 1. 更新原始的threadlist（隐藏的，但自动翻页需要）
				if (newContent.childNodes && newContent.childNodes.length > 0) {
					newContent.childNodes.forEach((child) =>
						currentContent.appendChild(child.cloneNode(true))
					);
				}

				// 2. 处理新布局的卡片
				setTimeout(() => {
					processNewContentForRedesignedLayout(newContent, redesignedContainer);
				}, 50);
			} else {
				// 原有逻辑：直接添加到当前容器
				if (newContent.childNodes && newContent.childNodes.length > 0) {
					newContent.childNodes.forEach((child) =>
						currentContent.appendChild(child.cloneNode(true))
					);
				} else if (newContent.innerHTML) {
					// 如果没有子节点，尝试直接添加HTML内容
					const tempDiv = document.createElement('div');
					tempDiv.innerHTML = newContent.innerHTML;
					while (tempDiv.firstChild) {
						currentContent.appendChild(tempDiv.firstChild);
					}
				}
			}
		}

		/**
		 * 为新布局处理新加载的内容
		 * @param {Element} newContent 新的内容元素
		 * @param {Element} redesignedContainer 新布局容器
		 */
		function processNewContentForRedesignedLayout(newContent, redesignedContainer) {
			// 获取新内容中的置顶帖子行
			const newStickyThreadRows = newContent.querySelectorAll('tbody[id^="stickthread_"]');

			// 获取新内容中的普通帖子行
			const newNormalThreadRows = newContent.querySelectorAll('tbody[id^="normalthread_"]');

			// 处理置顶帖（如果有的话）
			newStickyThreadRows.forEach(row => {
				const card = processThreadRowForAutoPagination(row, true);
				if (card) redesignedContainer.appendChild(card);
			});

			// 处理普通帖
			newNormalThreadRows.forEach(row => {
				const card = processThreadRowForAutoPagination(row, false);
				if (card) redesignedContainer.appendChild(card);
			});
		}

		/**
		 * 为自动翻页处理帖子行，生成卡片（复用discuz_redesign.js的逻辑）
		 * @param {Element} row 帖子行元素
		 * @param {boolean} isSticky 是否为置顶帖
		 * @returns {Element|null} 生成的卡片元素
		 */
		function processThreadRowForAutoPagination(row, isSticky = false) {
			// 提取帖子信息
			const titleElement = row.querySelector('a.s.xst');
			if (!titleElement) return null;

			const title = titleElement.textContent;
			const link = titleElement.href;

			// 提取帖子ID用于重复检查
			const threadId = extractThreadId(link);
			if (!threadId) return null;

			// 检查是否已经存在相同的帖子卡片
			const existingCard = document.querySelector(`.thread-card[data-thread-id="${threadId}"]`);
			if (existingCard) {
				console.log('跳过重复帖子:', title);
				return null;
			}

			// 提取作者信息
			const authorElement = row.querySelector('td.by cite a');
			const author = authorElement ? authorElement.textContent : '未知作者';

			// 提取space-uid并生成头像地址
			const spaceUid = extractSpaceUidForAutoPagination(row);
			const avatarUrl = generateAvatarUrlForAutoPagination(spaceUid);

			// 提取时间
			const timeElement = row.querySelector('td.by em span');
			const time = timeElement ? (timeElement.textContent || timeElement.getAttribute('title') || '') : '';

			// 提取回复数和查看数
			const replyElement = row.querySelector('td.num a');
			const viewElement = row.querySelector('td.num em');
			const replies = replyElement ? replyElement.textContent : '0';
			const views = viewElement ? viewElement.textContent : '0';

			// 创建卡片元素
			const card = document.createElement('div');
			card.className = 'thread-card' + (isSticky ? ' sticky' : '');
			card.setAttribute('data-thread-id', threadId); // 添加唯一标识

			// 构建卡片HTML
			if (isSticky) {
				// 置顶帖：只显示标题，不显示其他信息
				card.innerHTML = `
					<div class="thread-avatar">
						<img src="${avatarUrl}" alt="${author}" onerror="this.src='https://ttou.j03og.app/uc_server/images/noavatar_small.gif'">
					</div>
					<div class="thread-content">
						<div class="thread-title">
							<span class="sticky-tag">置顶</span>
							<a href="${link}">${title}</a>
						</div>
					</div>
				`;
			} else {
				// 普通帖：保持原有布局
				card.innerHTML = `
					<div class="thread-avatar">
						<img src="${avatarUrl}" alt="${author}" onerror="this.src='https://ttou.j03og.app/uc_server/images/noavatar_small.gif'">
					</div>
					<div class="thread-content">
						<div class="thread-title">
							<a href="${link}">${title}</a>
						</div>
						<div class="thread-info">
							<span class="thread-author">${author}</span>
							<span class="thread-time">${time}</span>
							<span class="thread-replies">回复: ${replies}</span>
							<span class="thread-views">查看: ${views}</span>
						</div>
					</div>
				`;
			}

			return card;
		}

		/**
		 * 从帖子链接中提取帖子ID
		 * @param {string} link 帖子链接
		 * @returns {string|null} 帖子ID
		 */
		function extractThreadId(link) {
			// 匹配 thread-123456-1-1.html 格式
			const threadMatch = link.match(/thread-(\d+)-/);
			if (threadMatch) {
				return threadMatch[1];
			}

			// 匹配 tid=123456 格式
			const tidMatch = link.match(/tid=(\d+)/);
			if (tidMatch) {
				return tidMatch[1];
			}

			return null;
		}

		/**
		 * 为自动翻页提取space-uid
		 * @param {Element} row 帖子行元素
		 * @returns {number|null} space-uid
		 */
		function extractSpaceUidForAutoPagination(row) {
			// 查找作者链接中的space-uid
			const authorLink = row.querySelector('td.by cite a');
			if (authorLink && authorLink.href) {
				const match = authorLink.href.match(/space-uid-(\d+)/);
				if (match) {
					return parseInt(match[1]);
				}
			}
			return null;
		}

		/**
		 * 为自动翻页根据space-uid生成头像地址
		 * @param {number|null} spaceUid space-uid
		 * @returns {string} 头像URL
		 */
		function generateAvatarUrlForAutoPagination(spaceUid) {
			// 如果没有spaceUid，返回默认头像
			if (!spaceUid) {
				return 'https://ttou.j03og.app/uc_server/images/noavatar_small.gif';
			}

			// 将uid转换为9位数字字符串，前面补0
			const uidStr = spaceUid.toString().padStart(9, '0');

			// 按每两位分组：000/42/81/19
			// 对于428119，补0后变成000428119，分组为：000/42/81/19
			const part1 = uidStr.slice(0, 3);   // 000
			const part2 = uidStr.slice(3, 5);   // 42
			const part3 = uidStr.slice(5, 7);   // 81
			const part4 = uidStr.slice(7, 9);   // 19

			// 构建头像URL
			const baseUrl = window.location.origin;
			return `${baseUrl}/uc_server/data/avatar/${part1}/${part2}/${part3}/${part4}_avatar_middle.jpg`;
		}

		/**
		 * 更新页面上的分页元素（页码）为新页面中的分页元素。
		 * @param {Element} newPgElement 新的分页元素
		 */
		function updatePagination(newPgElement) {
			const currentPageElements = document.querySelectorAll(".pg");
			currentPageElements.forEach(
				(pg) => (pg.innerHTML = newPgElement.innerHTML)
			);
		}

		/**
		 * 根据页面名称和设置处理页面内容。
		 * @param {string} pageName 页面名称
		 * @param {Object} settings 用户设置
		 */
		async function processPageContentBasedOnSettings(pageName, settings) {
			if (pageName == "isSearchPage") {
				filterElementsBasedOnSettings(settings);
				displayAdvanThreadImages(settings);
			} else if (pageName == "isForumDisplayPage") {
				// stylePosts 函数已移除，因为discuz_redesign.js已经改变了页面结构
				if (settings.enableTitleStyle) {
					console.log('标题样式功能已移除，因为discuz_redesign.js已经改变了页面结构');
				}
				removeAD2();
				blockingResolvedAction(settings);
				blockContentByTitle(settings);

				// 检查是否存在新布局
				const redesignedContainer = document.querySelector('.redesigned-thread-list');
				if (redesignedContainer) {
					// 如果存在新布局，只应用新布局的功能
					setTimeout(() => {
						blockContentInRedesignedLayout(settings);
						blockContentByTitleInRedesignedLayout(settings);
						blockingResolvedActionInRedesignedLayout(settings);
						if (settings.displayThreadImages) {
							displayThreadImagesInRedesignedLayout(settings);
						}
					}, 100);
				} else {
					// 原始布局的图片预览功能已移除
					console.log('原始布局的图片预览功能已移除，请使用新的卡片布局');
				}
			} else if (pageName == "isPostPage") {
				replacePMonPost();
				showAvatarEvent();
				const userid = getUserId();
				if (userid) {
					// addQuickGradeToPostButton();//已失效和谐
					addQuickActionToPostButton();
				}
				removeAD3();
				manipulateMedals(settings); // 修改用户勋章显示
			}
		}

		/**
		 * 检查页面内容是否已经填满视窗，如果没有，则加载下一页内容。
		 */
		function checkAndLoadIfContentNotEnough() {
			if (document.body.offsetHeight <= window.innerHeight && !isLoading) {
				loadNextPage();
			}
		}

		// 创建滚动事件处理函数，添加节流控制
		let scrollTimeout;
		const scrollHandler = () => {
			// 清除之前的定时器
			if (scrollTimeout) {
				clearTimeout(scrollTimeout);
			}

			// 设置新的定时器，延迟执行以避免过于频繁的触发
			scrollTimeout = setTimeout(() => {
				if (
					window.innerHeight + window.scrollY >=
						document.body.offsetHeight - 500 &&
					!isLoading
				) {
					loadNextPage();
				}
			}, 100); // 100ms的延迟
		};

		// 添加滚动事件监听器
		window.addEventListener("scroll", scrollHandler);

		// 保存清理函数到全局，以便在需要时清理
		window.infiniteScrollCleanup = () => {
			window.removeEventListener("scroll", scrollHandler);
			window.infiniteScrollInitialized = false;
			window.infiniteScrollCleanup = null;
		};

		checkAndLoadIfContentNotEnough();
	}

	/**
	 * 根据提供的设置过滤页面上的元素
	 * @param {Object} settings 用户设置
	 */
	function filterElementsBasedOnSettings(settings) {
		const pbwElements = document.querySelectorAll(".pbw");

		pbwElements.forEach((pbw) => {
			let shouldDisplay = shouldElementBeDisplayed(pbw, settings);
			pbw.style.display = shouldDisplay ? "block" : "none";
		});
	}

	/**
	 * 确定给定的元素是否应该根据提供的设置显示在页面上
	 * @param {Element} element 待检查的元素
	 * @param {Object} settings 用户设置
	 * @returns {boolean} 根据设置是否应显示元素
	 */
	function shouldElementBeDisplayed(element, settings) {
		if (settings.TIDGroup && settings.TIDGroup.length) {
			const aElement = element.querySelector(".xi1");
			if (!aElement || !doesTIDGroupMatch(aElement, settings.TIDGroup)) {
				return false;
			}
		}

		if (settings.excludeGroup && settings.excludeGroup.length) {
			const pElement = element.querySelector("p:nth-of-type(2)");
			const xs3Element = element.querySelector(".xs3");

			if (
				isExcludedByKeyword(pElement, settings.excludeGroup) ||
				isExcludedByKeyword(xs3Element, settings.excludeGroup)
			) {
				return false;
			}
		}

		return true;
	}

	/**
	 * 检查给定的元素链接是否与提供的TIDGroup中的任何ID匹配
	 * @param {Element} aElement 待检查的链接元素
	 * @param {Array} TIDGroup TID组
	 * @returns {boolean} 是否与TID组匹配
	 */
	function doesTIDGroupMatch(aElement, TIDGroup) {
		const href = aElement.getAttribute("href");

		// 判断是否匹配 fid=${tid} 或 forum=${tid} 的格式
		return TIDGroup.some(
			(tid) => href.includes(`fid=${tid}`) || href.includes(`forum-${tid}`)
		);
	}

	/**
	 * 检查给定的元素内容是否包含提供的排除关键字列表中的任何关键字
	 * @param {Element} element 待检查的元素
	 * @param {Array} excludeGroup 排除关键字组
	 * @returns {boolean} 是否包含关键字
	 */
	function isExcludedByKeyword(element, excludeGroup) {
		if (!element) return false;
		const text = element.textContent.toLowerCase();
		return excludeGroup.some((keyword) => text.includes(keyword.toLowerCase()));
	}

	// #endregion

	// #region 帖子列表页执行的方法

	/**
	 * 显示发帖模态框
	 * @param {number} fid - 板块ID
	 */
	function showPostModal(fid) {
		// 检查是否已存在模态框
		const existingModal = document.getElementById('postModal');
		if (existingModal) {
			existingModal.remove();
		}

		// 创建模态框
		const modal = document.createElement('div');
		modal.id = 'postModal';

		// 创建关闭按钮
		const closeBtn = document.createElement('button');
		closeBtn.className = 'close-btn';
		closeBtn.onclick = function() {
			modal.remove();
		};

		// 创建iframe来加载发帖页面
		const iframe = document.createElement('iframe');
		iframe.src = `forum.php?mod=post&action=newthread&fid=${fid}`;
		iframe.style.cssText = `
			width: 100%;
			height: 100%;
      border: none;
		`;

		// 组装模态框
		modal.appendChild(closeBtn);
		modal.appendChild(iframe);
		document.body.appendChild(modal);

		// 添加拖动功能
		enableDrag(modal);

		// iframe加载完成后的处理
		iframe.onload = function() {
			try {
				// 尝试隐藏iframe中不需要的元素（如果同域的话）
				const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
				if (iframeDoc) {
					// 隐藏导航栏等不需要的元素
					const elementsToHide = [
						'#hd', // 头部
						'#ft', // 底部
						'#pt', // 面包屑导航
						'.wp .cl', // 某些容器
					];

					elementsToHide.forEach(selector => {
						const element = iframeDoc.querySelector(selector);
						if (element) {
							element.style.display = 'none';
						}
					});

					// 调整iframe内容的样式
					const style = iframeDoc.createElement('style');
					style.textContent = `
						/* 设置body为flex容器来居中内容 */
						body {
							margin: 0;
							padding: 0;
							display: flex;
							justify-content: center;
							align-items: center;
							min-height: 100vh;
							background: transparent;
						}

						/* 确保主容器正常显示 */
						#ct {
							margin: 0;
							position: relative;
							z-index: 1;
						}

						.bm { margin: 10px 0; }

						/* 修改#nv_forum #ct的宽度为1000px */
						#nv_forum #ct {
							width: 1000px !important;
						}

						/* 引入原有的模态框样式 */
						#organizeModal {
							line-height: 2.5;
							font-size: 13px;
							width: 650px;
							border: none;
							left: 50%;
							top: 50%;
							transform: translate(-50%, -50%) scale(1);
							border-radius: 10px;
							padding: 20px;
							z-index: 1000;
							position: fixed;
							display: none;
							cursor: move;
							user-select: none;
							backdrop-filter: blur(30px) saturate(180%);
							border: 1px solid rgb(255, 255, 255);
							box-shadow: 0 1px 50px rgba(0, 0, 0, 0.20), inset 0px 0px 90px 10px rgba(255, 255, 255, 0.9);
							background-color: rgba(244, 246, 248, 0.85);
						}

						/* 引入原有的按钮样式 */
						#shareBtn, #organizeBtn {
							color: #fff;
							background-color: var(--button);
							border: none;
							height: 25px;
							width: 50px;
							margin-left: 20px;
							border-radius: var(--icon-radius, 4px);
						}

						/* 输入框样式 */
						#resourceName,
						#resourceSize,
						#videoCount,
						#imageCount,
						#quota,
						#resourceLink {
							border: 1px solid #666;
							border-radius: 6px;
							margin-left: 4px;
						}

						#resourceName,
						#resourceLink {
							width: 300px;
						}

						/* 插入按钮样式 */
						#insetBtn {
							border: none;
							cursor: pointer;
							border-radius: 6px;
							margin: 10px 0 0 7px;
							background-color: #000;
							color: #fff;
							box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
							height: 25px;
							width: 55px;
							font-size: 13px;
							padding: 0 8px;
						}

						/* 关闭按钮样式 */
						.close-btn {
							position: absolute;
							top: 10px;
							right: 15px;
							cursor: pointer;
							width: 20px;
							height: 20px;
							border-radius: 50%;
							border: none;
							background-color: #000;
							transition: 0.15s;
							box-shadow: 0 1px 6px rgba(0, 0, 0, 0.15);
						}

						.close-btn:hover {
							background-color: red;
						}

						.close-btn-line {
							position: absolute;
							width: 12px;
							height: 2px;
							background: white;
							transform-origin: center;
							top: 50%;
							left: 50%;
						}

						.close-btn-line:first-child {
							transform: translate(-50%, -50%) rotate(45deg);
						}

						.close-btn-line:last-child {
							transform: translate(-50%, -50%) rotate(-45deg);
						}
					`;
					iframeDoc.head.appendChild(style);

					// 在iframe中初始化发帖页功能
					const initIframeFeatures = () => {
						console.log('开始初始化iframe功能');
						console.log('iframe URL:', iframeDoc.location.href);

						// 检查是否是发帖页面
						const isPostPageInIframe = iframeDoc.location.href.includes("forum.php?mod=post&action=newthread");
						console.log('是否为发帖页面:', isPostPageInIframe);

						if (isPostPageInIframe) {
							console.log('开始在iframe中执行PostContent功能');

							// 在iframe的window对象上下文中执行PostContent函数
							const iframeWindow = iframe.contentWindow;

							// 直接在iframe的document上下文中执行原有的PostContent逻辑
							try {
								// 直接执行PostContent的核心逻辑，不修改全局document
								console.log('开始执行PostContent逻辑');

								// 检查URL条件
								if (iframeDoc.location.href.includes("forum.php?mod=post&action=newthread")) {
									console.log('URL条件满足，开始创建元素');

									// 创建发帖须知链接
									const link = iframeDoc.createElement("a");
									link.href = "/forum.php?mod=redirect&goto=findpost&ptid=1708826&pid=16039784";
									link.textContent = "发帖须知";
									link.target = "_blank";

									// 创建整理按钮
									const organizeButton = iframeDoc.createElement("li");
									organizeButton.className = "a";
									organizeButton.innerHTML = '<button id="organizeBtn" type="button">整理</button>';

									// 创建自转按钮
									const shareButton = iframeDoc.createElement("li");
									shareButton.className = "a";
									shareButton.innerHTML = '<button id="shareBtn" type="button">自转</button>';

									// 查找插入位置（使用原始PostContent的选择器）
									const ulElement = iframeDoc.querySelector(".tb.cl.mbw");
									console.log('找到目标ul元素:', ulElement);

									if (ulElement) {
										console.log('插入按钮到页面');
										ulElement.appendChild(link);
										ulElement.appendChild(organizeButton);
										ulElement.appendChild(shareButton);
									} else {
										console.warn("未找到指定的ul元素(.tb.cl.mbw)");
										// 尝试备用选择器
										const backupElement = iframeDoc.querySelector("#postbox .area");
										if (backupElement && backupElement.parentNode) {
											console.log('使用备用插入位置');
											backupElement.parentNode.insertBefore(link, backupElement);
											backupElement.parentNode.insertBefore(organizeButton, backupElement);
											backupElement.parentNode.insertBefore(shareButton, backupElement);
										}
									}

									// 创建模态框HTML（使用原有的完整HTML结构）
									const modalContent = `
									<div id="organizeModal">
										<!-- 关闭按钮 -->
										<button class="close-btn">
											<span class="close-btn-line"></span>
											<span class="close-btn-line"></span>
										</button>

										<div>
											<strong>【资源名称】：</strong>
											<input type="text" id="resourceName"/>
										</div>
										<div>
											<strong>【资源类型】：</strong>
											<label><input type="radio" name="resourceType" value="影片" checked />影片</label>
											<label><input type="radio" name="resourceType" value="视频" />视频</label>
											<label><input type="radio" name="resourceType" value="动漫" />动漫</label>
											<label><input type="radio" name="resourceType" value="套图" />套图</label>
											<label><input type="radio" name="resourceType" value="游戏" />游戏</label>
										</div>
										<div>
											<strong>【是否有码】：</strong>
											<label><input type="radio" name="censorship" value="有码" checked />有码</label>
											<label><input type="radio" name="censorship" value="无码" />无码</label>
										</div>
										<div>
											<strong>【是否水印】：</strong>
											<label><input type="radio" name="watermark" value="有水印" />有水印</label>
											<label><input type="radio" name="watermark" value="无水印" checked />无水印</label>
										</div>
										<div>
											<strong>【字幕选项】：</strong>
											<label><input type="radio" name="subtitle" value="中文字幕" />中文字幕</label>
											<label><input type="radio" name="subtitle" value="日文字幕" />日文字幕</label>
											<label><input type="radio" name="subtitle" value="英文字幕" />英文字幕</label>
											<label><input type="radio" name="subtitle" value="无字幕" checked />无字幕</label>
										</div>
										<div>
											<strong>【资源大小】：</strong>
											<input type="text" id="resourceSize" placeholder=""/>
											<label><input type="radio" name="sizeUnit" value="M" />M</label>
											<label><input type="radio" name="sizeUnit" value="G" checked />G</label>
											<label><input type="radio" name="sizeUnit" value="T" />T</label>
										</div>
										<div>
											<strong>【下载类型】：</strong>
											<label><input type="radio" name="downType" value="115ED2K" checked />115ED2K</label>
											<label><input type="radio" name="downType" value="BT/磁链" />BT/磁链</label>
											<label><input type="radio" name="downType" value="ED2K" />ED2K</label>
											<label><input type="radio" name="downType" value="夸克网盘" />夸克网盘</label>
											<label><input type="radio" name="downType" value="百度网盘" />百度网盘</label>
											<label><input type="radio" name="downType" value="PikPak网盘" />PikPak网盘</label>
											<label><input type="radio" name="downType" value="其它网盘" />其它网盘</label>
										</div>
										【视频数量】：<input type="text" id="videoCount" placeholder=""/><br>
										【图片数量】：<input type="text" id="imageCount" placeholder=""/><br>
										【配额数量】：<input type="text" id="quota" placeholder=""/>
										<div><strong>【资源预览】：</strong></div>
										<div><strong>【资源链接】：</strong><input type="text" id="resourceLink"/></div>
										<button id="insetBtn" type="button">插入</button>
									</div>`;

									console.log('插入模态框HTML');
									iframeDoc.body.insertAdjacentHTML("beforeend", modalContent);

									// 添加拖动功能
									const organizeModalElement = iframeDoc.getElementById("organizeModal");
									if (organizeModalElement) {
										organizeModalElement.style.position = "fixed";
										organizeModalElement.style.cursor = "move";
										// 注入enableDrag函数到iframe
										iframeWindow.enableDrag = enableDrag;
										iframeWindow.enableDrag(organizeModalElement);
									}

									// 添加事件监听器
									console.log('添加事件监听器');
									let ttttype = '';

									const organizeBtn = iframeDoc.getElementById("organizeBtn");
									const shareBtn = iframeDoc.getElementById("shareBtn");
									const insertBtn = iframeDoc.getElementById("insetBtn");
									const closeBtn = iframeDoc.querySelector("#organizeModal .close-btn");

									if (organizeBtn) {
										organizeBtn.addEventListener("click", function() {
											console.log('点击整理按钮');
											ttttype = "整理";
											const modal = iframeDoc.getElementById("organizeModal");
											modal.style.display = "block";
											if (!modal.dataset.hasBeenDragged) {
												modal.style.left = "50%";
												modal.style.top = "50%";
												modal.style.transform = "translate(-50%, -50%)";
											}
										});
									}

									if (shareBtn) {
										shareBtn.addEventListener("click", function() {
											console.log('点击自转按钮');
											ttttype = "自转";
											const modal = iframeDoc.getElementById("organizeModal");
											modal.style.display = "block";
											if (!modal.dataset.hasBeenDragged) {
												modal.style.left = "50%";
												modal.style.top = "50%";
												modal.style.transform = "translate(-50%, -50%)";
											}
										});
									}

									if (closeBtn) {
										closeBtn.addEventListener("click", function() {
											iframeDoc.getElementById("organizeModal").style.display = "none";
										});
									}

									if (insertBtn) {
										insertBtn.addEventListener("click", function() {
											console.log('点击插入按钮');
											// 这里使用原有的插入逻辑
											const resourceName = iframeDoc.getElementById("resourceName").value;
											const resourceType = iframeDoc.querySelector('input[name="resourceType"]:checked')?.value;
											const censorship = iframeDoc.querySelector('input[name="censorship"]:checked')?.value;
											const watermark = iframeDoc.querySelector('input[name="watermark"]:checked')?.value;
											const subtitle = iframeDoc.querySelector('input[name="subtitle"]:checked')?.value;
											const resourceLink = iframeDoc.getElementById("resourceLink").value;
											const downType = iframeDoc.querySelector('input[name="downType"]:checked')?.value;
											const resourceSize = iframeDoc.getElementById("resourceSize").value;
											const sizeUnit = iframeDoc.querySelector('input[name="sizeUnit"]:checked').value;
											const videoCount = iframeDoc.getElementById("videoCount").value;
											const imageCount = iframeDoc.getElementById("imageCount").value;
											const quota = iframeDoc.getElementById("quota").value;

											let resourceSizeStr = resourceSize ? `${resourceSize}${sizeUnit}` : "";
											let videoCountStr = videoCount ? `${videoCount}V` : "";
											let imageCountStr = imageCount ? `${imageCount}P` : "";
											let quotaStr = quota ? `${quota}配额` : "";

											const content = `
											【资源名称】：${resourceName}<br>
											【资源类型】：${resourceType}<br>
											【是否有码】：${censorship} @ ${watermark} @ ${subtitle}<br>
											【资源大小】：${resourceSizeStr}/${videoCountStr}/${imageCountStr}/${quotaStr}<br>
											【资源预览】：<br>
											【资源链接】：<div class="blockcode"><blockquote>${resourceLink}</blockquote></div><br>
											`;

											// 插入到编辑器
											const iframe_editor = iframeDoc.querySelector(".area iframe");
											if (iframe_editor && iframe_editor.contentDocument) {
												const body = iframe_editor.contentDocument.body;
												if (body && body.isContentEditable) {
													body.innerHTML = content;
												}
											}

											// 设置标题
											const title = `【${ttttype}】【${downType}】${resourceName}【${resourceSizeStr}/${videoCountStr}/${imageCountStr}/${quotaStr}】`;
											const subjectInput = iframeDoc.getElementById("subject");
											if (subjectInput) {
												subjectInput.value = title;
											}

											// 设置分类
											const selectElement = iframeDoc.getElementById("typeid");
											if (selectElement) {
												selectElement.setAttribute("selecti", "8");
											}
											const aElement = iframeDoc.querySelector(".ftid a#typeid_ctrl");
											if (aElement) {
												aElement.textContent = "情色分享";
												aElement.setAttribute("initialized", "true");
											}

											iframeDoc.getElementById("organizeModal").style.display = "none";
										});
									}

									console.log('PostContent功能初始化完成');
								}

							} catch (e) {
								console.error('PostContent执行出错:', e);
							}

							// 添加右侧快捷按钮
							addIframeQuickButtons(iframeDoc, iframeWindow);
						}
					};

					// 页面加载完成后初始化功能
					if (iframeDoc.readyState === 'complete') {
						initIframeFeatures();
					} else {
						iframeDoc.addEventListener('DOMContentLoaded', initIframeFeatures);
						iframe.addEventListener('load', initIframeFeatures);
					}

					// 修复弹出窗口定位问题
					const fixPopupPositioning = () => {
						// 监听DOM变化，当有新的弹出窗口出现时进行修正
						const observer = new MutationObserver((mutations) => {
							mutations.forEach((mutation) => {
								mutation.addedNodes.forEach((node) => {
									if (node.nodeType === 1) { // 元素节点
										// 查找所有可能的弹出窗口
										const popups = node.querySelectorAll ?
											node.querySelectorAll('.popupmenu_popup, .popupmenu, .tip, .tooltip, [id*="menu"], [class*="popup"], [class*="dropdown"]') :
											[];

										// 如果节点本身就是弹出窗口
										if (node.classList && (
											node.classList.contains('popupmenu_popup') ||
											node.classList.contains('popupmenu') ||
											node.classList.contains('tip') ||
											node.classList.contains('tooltip') ||
											node.id.includes('menu') ||
											node.className.includes('popup') ||
											node.className.includes('dropdown')
										)) {
											popups.push(node);
										}

										// 修正弹出窗口的定位
										popups.forEach((popup) => {
											if (popup.style.position === 'absolute') {
												// 获取当前位置
												const rect = popup.getBoundingClientRect();

												// 计算相对于视口的位置
												popup.style.position = 'fixed';
												popup.style.left = rect.left + 'px';
												popup.style.top = rect.top + 'px';
												popup.style.zIndex = '9999';
											}
										});
									}
								});
							});
						});

						observer.observe(iframeDoc.body, {
							childList: true,
							subtree: true
						});
					};

					// 页面加载完成后启动修复
					if (iframeDoc.readyState === 'complete') {
						fixPopupPositioning();
					} else {
						iframeDoc.addEventListener('DOMContentLoaded', fixPopupPositioning);
					}
				}
			} catch (e) {
				// 如果跨域，无法访问iframe内容，这是正常的
				console.log('无法访问iframe内容（可能是跨域限制）');
			}
		};
	}

	/**
	 * 创建"快速发帖"按钮，用于快速本板块发帖
	 * @return {HTMLElement} 按钮元素
	 */
	function createFastPostButton() {
		return createButton(
			"fastPostButtonInternalId", // 内部使用的ID，不作为HTML id
			"快速发帖",
			function () {
				let fid = getFidFromElement();
				showPostModal(fid);
			},
			undefined, // className
			undefined, // style
			{ id: "fastPostButton" } // attributes - 这将设置HTML id
		);
	}

	/**
	 * 创建时间排序按钮
	 * @param {Object} settings - 用户的设置
	 * @param {Element} buttonContainer - 按钮容器元素
	 */
	function createTimeSortButton(settings, buttonContainer) {
		const currentURL = window.location.href;
		const queryParams = getQueryParams(currentURL);
		const fid = queryParams.fid;
		const hasOrderBy = queryParams.orderby === "dateline";
		const isFidInOrder = settings.orderFids.includes(fid);

		const setText = (isOrder) => {
			return isOrder ? "时间排序" : "默认排序";
		};

		const initialButtonText = setText(isFidInOrder);

		const timeSortButton = createButton(
			"timeSortButton",
			initialButtonText,
			function () {
				if (isFidInOrder) {
					timeSortButton.innerText = setText(false);
					if (hasOrderBy) {
						const newURL = `${baseURL}/forum.php?mod=forumdisplay&fid=${fid}`;
						window.location.href = newURL;
					}
					settings.orderFids = settings.orderFids.filter(
						(existingFid) => existingFid !== fid
					);
				} else {
					timeSortButton.innerText = setText(true);
					if (!hasOrderBy) {
						const newURL = `${baseURL}/forum.php?mod=forumdisplay&fid=${fid}&filter=author&orderby=dateline`;
						window.location.href = newURL;
					}
					settings.orderFids.push(fid);
				}
				GM_setValue("orderFids", JSON.stringify(settings.orderFids));
			}
		);

		buttonContainer.appendChild(timeSortButton);
	}

	/**
	 * 处理帖子列表页面的初始状态，可能会重定向
	 * @param {Object} settings - 用户的设置
	 */
	function handleInitialPageState(settings) {
		const currentURL = window.location.href;
		const queryParams = getQueryParams(currentURL);
		const fid = queryParams.fid;
		const hasOrderBy = queryParams.orderby === "dateline";

		// 检查当前fid是否存在于orderFids中
		const isFidInOrder = settings.orderFids.includes(fid);

		if (isFidInOrder && !hasOrderBy) {
			// 如果上次是时间排序，但现在URL没有orderby=dateline，则需要重定向
			const newURL = `${baseURL}/forum.php?mod=forumdisplay&fid=${fid}&filter=author&orderby=dateline`;
			window.location.href = newURL;
		} else if (!isFidInOrder && hasOrderBy) {
			// 如果上次是默认排序，但现在URL有orderby=dateline，则需要重定向
			const newURL = `${baseURL}/forum.php?mod=forumdisplay&fid=${fid}`;
			window.location.href = newURL;
		}
	}

	/**
	 * 在新的卡片布局中显示图片预览
	 * @param {object} settings - 设置对象
	 */
	async function displayThreadImagesInRedesignedLayout(settings) {
		if (!settings.displayThreadImages) {
			return;
		}

		const threadCards = document.querySelectorAll('.thread-card');

		if (threadCards.length === 0) {
			return;
		}

		let processedCount = 0;
		for (let card of threadCards) {
			// 跳过被屏蔽的帖子
			if (card.style.display === 'none' ||
				card.innerHTML.includes('已屏蔽主题')) {
				continue;
			}

			// 跳过置顶贴
			if (isStickyThread(card)) {
				continue;
			}

			// 获取链接元素 - 优先使用最后一个链接（通常是主标题链接）
			const linkElements = card.querySelectorAll('.thread-title a');
			const linkElement = linkElements[linkElements.length - 1]; // 取最后一个链接
			if (!linkElement) {
				continue;
			}

			// 检查是否已经添加了图片预览（更严格的检查）
			if (card.querySelector('.thread-image-preview') || card.dataset.imageProcessed === 'true') {
				continue;
			}

			// 标记为正在处理，避免重复处理
			card.dataset.imageProcessed = 'true';

			let threadURL = linkElement.href;

			// 处理重定向链接，转换为正常的帖子查看链接
			if (threadURL.includes('mod=redirect')) {
				const tidMatch = threadURL.match(/tid=(\d+)/);
				if (tidMatch) {
					const tid = tidMatch[1];
					// 转换为正常的帖子查看链接（第一页）
					threadURL = `${window.location.origin}/forum.php?mod=viewthread&tid=${tid}`;
				}
			}

			// 验证URL是否为帖子内页链接
			if (!threadURL || (!threadURL.includes('thread-') && !threadURL.includes('tid='))) {
				card.dataset.imageProcessed = 'false'; // 重置标记
				continue;
			}

			try {
				let response = await fetch(threadURL);

				if (!response.ok) {
					continue;
				}

				let pageContent = await response.text();
				let parser = new DOMParser();
				let doc = parser.parseFromString(pageContent, "text/html");
				let imgElements = doc.querySelectorAll("img.zoom");

				// 过滤图片
				imgElements = Array.from(imgElements)
					.filter((img) => {
						let fileValue = img.getAttribute("file");
						return (
							fileValue &&
							!fileValue.includes("static") &&
							!fileValue.includes("hrline")
						);
					})
					.slice(0, 3);

				if (!imgElements.length) {
					continue;
				}

				// 创建图片预览容器
				const imgContainer = document.createElement("div");
				imgContainer.className = "thread-image-preview";

				let validImageCount = 0;
				imgElements.forEach((imgEl) => {
					let imgSrc = imgEl.getAttribute("file");
					if (!imgSrc) {
						return;
					}

					let img = document.createElement("img");
					img.src = imgSrc;

					// 添加图片加载错误处理
					img.onerror = function() {
						this.style.display = 'none';
					};

					// 添加点击放大功能
					img.addEventListener('click', function() {
						window.open(this.src, '_blank');
					});

					imgContainer.appendChild(img);
					validImageCount++;
				});

				if (validImageCount > 0) {
					// 将图片容器添加到卡片内容区域
					const threadContent = card.querySelector('.thread-content');
					if (threadContent) {
						threadContent.appendChild(imgContainer);
						processedCount++;
					} else {
						// 如果找不到 .thread-content，尝试添加到卡片末尾
						card.appendChild(imgContainer);
						processedCount++;
					}
				}
			} catch (error) {
				// 移除处理标记，以便下次重试
				card.dataset.imageProcessed = 'false';
			}
		}
	}

	/**
	 * 插入帖子内的前三张图片到帖子标题下方
	 * 注意：此函数已被重构，原始布局的图片预览逻辑已移除
	 * 现在只支持新的卡片布局
	 */
	async function displayThreadImages(settings) {
		if (!settings.displayThreadImages) {
			console.log('🖼️ [图片预览] 功能已禁用');
			return;
		}

		console.log('🖼️ [图片预览] 开始检查布局类型');

		// 检测是否使用了新的布局
		if (isRedesignedLayout()) {
			console.log('🖼️ [图片预览] 检测到新布局，执行图片预览');
			await displayThreadImagesInRedesignedLayout(settings);
			return;
		}

		// 原始布局的图片预览功能已移除，因为discuz_redesign.js已经改变了页面结构
		console.log('🖼️ [图片预览] 原始布局的图片预览功能已移除，请使用新的卡片布局');
	}

	/**
	 * 处理帖子列表页面，设置页面状态、样式、内容屏蔽、时间排序和无限滚动
	 * @param {Object} settings - 用户的设置
	 * @param {Element} buttonContainer - 按钮容器元素
	 */
	function handleForumDisplayPage(settings, buttonContainer) {
		handleInitialPageState(settings);
		// stylePosts 函数已移除，因为discuz_redesign.js已经改变了页面结构
		if (settings.enableTitleStyle) {
			console.log('标题样式功能已移除，因为discuz_redesign.js已经改变了页面结构');
		}
		removeAD2();
		blockingResolvedAction(settings);
		removeFastPost();
		createTimeSortButton(settings, buttonContainer);
		blockContentByTitle(settings);

		// 图片预览功能已移至baseFunction的重试机制中，确保在新布局完全生成后执行
		const currentURL = window.location.href;
		const queryParams = getQueryParams(currentURL);
		const fid = queryParams.fid;
		if (fid == 143 || fid == "143") {
			const blockingResolvedText =
				settings.blockingResolved == true ? "显示解决" : "屏蔽解决";
			const blockingResolvedButton = createButton(
				"blockingResolvedBtn",
				blockingResolvedText,
				function () {
					if (blockingResolvedButton.innerText === "显示解决") {
						blockingResolvedButton.innerText = "屏蔽解决";
						GM_setValue("blockingResolved", false);
						location.reload();
					} else {
						blockingResolvedButton.innerText = "显示解决";
						GM_setValue("blockingResolved", true);
						location.reload();
					}
				}
			);

			buttonContainer.appendChild(blockingResolvedButton);
		}

		const userid = getUserId();
		if (userid && settings.showFastPost) {
			buttonContainer.appendChild(createFastPostButton());
		}
		initInfiniteScroll("isForumDisplayPage");
	}

	/**
	 * 移除帖子列表页广告
	 */
	async function removeAD2() {
		document.querySelectorAll(".show-text2").forEach((element) => {
			element.remove();
		});
	}

	/**
	 * 移除列表底部的快速发帖
	 */
	function removeFastPost() {
		document.querySelectorAll("#f_pst").forEach((element) => {
			element.remove();
		});
	}

	/**
	 * 在新布局中移除已解决的帖子
	 * @param {object} settings - 设置对象
	 */
	function blockingResolvedActionInRedesignedLayout(settings) {
		if (!settings.blockingResolved) {
			return;
		}

		const threadCards = document.querySelectorAll('.thread-card');
		threadCards.forEach((card) => {
			const titleElement = card.querySelector('.thread-title a');
			if (titleElement && titleElement.textContent.includes("[已解决]")) {
				card.remove();
			}
		});
	}

	/**
	 * 移除帖子列表页已解决的帖子
	 * 注意：原始布局的"已解决"帖子屏蔽逻辑已移除，现在只支持新的卡片布局
	 */
	async function blockingResolvedAction(settings) {
		if (!settings.blockingResolved) {
			return;
		}

		// 检测是否使用了新的布局
		if (isRedesignedLayout()) {
			blockingResolvedActionInRedesignedLayout(settings);
			return;
		}

		// 原始布局的"已解决"帖子屏蔽功能已移除，因为discuz_redesign.js已经改变了页面结构
		console.log('原始布局的"已解决"帖子屏蔽功能已移除，请使用新的卡片布局');
	}

	// #endregion

	// #region 搜索页执行的方法

	/**
	 * 处理搜索页面，包括增加高级搜索、添加页码、基于设置过滤元素和初始化无限滚动
	 * @param {Object} settings - 用户的设置
	 */
	function handleSearchPage(settings) {
		replaceImageSrc();
		addAdvancedSearch(settings);
		addPageNumbers();
		filterElementsBasedOnSettings(settings);
		initInfiniteScroll("isSearchPage");
		displayAdvanThreadImages(settings);
	}

	/**
	 * 替换搜索页面的logo。
	 */
	function replaceImageSrc() {
		// 等待页面完全加载
		window.addEventListener("load", function () {
			// 查找所有包含旧图片路径的img元素
			document
				.querySelectorAll(
					'img[src="static/image/common/logo_sc_s.png"]'
				)
				.forEach(function (img) {
					// 替换为新的图片路径
					img.src = "static/image/common/logo.png";
				});
		});
	}

	/**
	 * 插入帖子内的前三张图片到帖子标题下方（搜索页面专用）
	 */
	async function displayAdvanThreadImages(settings) {
		if (!settings.displayThreadImages) {
			return;
		}
		const h3Elements = document.querySelectorAll("h3.xs3");

		for (let h3Element of h3Elements) {
			const aElement = h3Element.querySelector("a");
			if (aElement) {
				let url = aElement.href;
				try {
					let response = await fetch(url);
					let pageContent = await response.text();
					let parser = new DOMParser();
					let doc = parser.parseFromString(pageContent, "text/html");
					let imgElements = doc.querySelectorAll("img.zoom");

					// 过滤图片
					imgElements = Array.from(imgElements)
						.filter((img) => {
							let fileValue = img.getAttribute("file");
							return (
								fileValue &&
								!fileValue.includes("static") &&
								!fileValue.includes("hrline")
							);
						})
						.slice(0, 3);

					if (!imgElements.length) continue;
					const closestLi = h3Element.closest("li");
					if (closestLi.querySelector("tbody, #imagePreviewTbody")) {
						continue;
					}

					// 创建新的图片容器
					const newTbody = document.createElement("tbody");
					newTbody.id = "imagePreviewTbody"; // Assigning the unique ID to the tbody
					const newTr = document.createElement("tr");
					const newTd = document.createElement("td");
					const imgContainer = document.createElement("div");
					imgContainer.style.display = "flex";

					imgElements.forEach((imgEl) => {
						let img = document.createElement("img");
						img.src = imgEl.getAttribute("file");
						img.style.width = "300px";
						img.style.height = "auto";
						img.style.maxWidth = "300px";
						img.style.maxHeight = "300px";
						img.style.marginRight = "10px";
						imgContainer.appendChild(img);
					});

					newTd.appendChild(imgContainer);
					newTr.appendChild(newTd);
					newTbody.appendChild(newTr);

					// h3Element.closest("li").after(newTbody);
					if (closestLi) {
						closestLi.appendChild(newTbody);
					}
				} catch (e) {
					console.error("Error fetching or processing:", e);
				}
			}
		}
	}

	// #endregion

	// #region 帖子内容页执行方法

	/**
	 * 创建"复制内容"按钮，用于快速复制本帖内容
	 * @return {HTMLElement} 按钮元素
	 */
	function createFastCopyButton() {
		return createButton("fastCopyButton", "复制帖子", function () {
			var content = document.querySelector(".t_f");
			var secondContent = document.querySelectorAll(".t_f")[1];
			var resultHtml = "";
			if (content) {
				resultHtml += processContent(content);
			}
			if (secondContent && secondContent.querySelectorAll("img").length > 3) {
				resultHtml += processContent(secondContent);
			}
			if (resultHtml !== "") {
				copyToClipboard(resultHtml);
				copyToClipboard(
					resultHtml,
					() => showTooltip("内容已复制!"),
					(err) => showTooltip("复制失败: ", err)
				);
			} else {
				showTooltip("复制失败: 没有找到相应内容");
			}
		});
	}

	/**
	 * 处理指定的内容
	 * @param {string} content html文本
	 * @return {cleanedHtml} 处理好的内容
	 */

	function processContent(content) {
		var html = content.innerHTML;
		var cleanedHtml = removeElementsByClass(
			html,
			["pstatus", "tip_4"],
			[
				"font",
				"div",
				"ignore_js_op",
				"br",
				"ol",
				"li",
				"strong",
				"a",
				"i",
				"table",
				"tbody",
				"tr",
				"td",
				"blockquote",
			],
			["em"]
		);
		cleanedHtml = removeNbspAndNewlines(cleanedHtml);
		cleanedHtml = removeElementsByIdPrefix(cleanedHtml, "attach_");

		return cleanedHtml;
	}

	/**
	 * 移除不需要的内容
	 * @param {string} htmlString html文本
	 * @return {stringWithoutNbsp} 链接
	 */

	function removeNbspAndNewlines(htmlString) {
		var stringWithoutNbsp = htmlString.replace(/&nbsp;/g, "");
		stringWithoutNbsp = stringWithoutNbsp.replace(/&amp;/g, "");
		stringWithoutNbsp = stringWithoutNbsp.replace(/\n+/g, "\n");
		stringWithoutNbsp = stringWithoutNbsp.replace(/\\baoguo/g, "\n");
		return stringWithoutNbsp;
	}

	/**
	 * 处理指定的内容
	 * @param {string} htmlString html文本
	 * @param {string} classList class列表
	 * @param {string} tagsToRemove tags
	 * @param {string} tagsToAllRemove tags
	 * @return {doc.body.innerHTML} 处理好的内容
	 */

	function removeElementsByClass(
		htmlString,
		classList,
		tagsToRemove,
		tagsToAllRemove
	) {
		var parser = new DOMParser();
		var doc = parser.parseFromString(htmlString, "text/html");
		classList.forEach(function (className) {
			var elements = doc.querySelectorAll("." + className);
			elements.forEach(function (element) {
				element.parentNode.removeChild(element);
			});
		});
		tagsToRemove.forEach(function (tagName) {
			var elements = doc.querySelectorAll(tagName);
			elements.forEach(function (element) {
				while (element.firstChild) {
					element.parentNode.insertBefore(element.firstChild, element);
				}
				element.parentNode.removeChild(element);
			});
		});
		tagsToAllRemove.forEach(function (tagName) {
			var elements = doc.querySelectorAll(tagName);

			elements.forEach(function (element) {
				element.parentNode.removeChild(element);
			});
		});
		var imgElements = doc.querySelectorAll("img");
		imgElements.forEach(function (img) {
			var fileAttr = img.getAttribute("file");
			if (fileAttr) {
				var fileText =
					(fileAttr.includes("static/image") ? "" : fileAttr) + "\\baoguo";
				var textNode = document.createTextNode(fileText);
				img.parentNode.replaceChild(textNode, img);
			} else {
				var srcAttr = img.getAttribute("src");
				if (srcAttr) {
					var srcText =
						(srcAttr.includes("static/image") ? "" : srcAttr) + "\\baoguo";
					var textNode1 = document.createTextNode(srcText);
					img.parentNode.replaceChild(textNode1, img);
				}
			}
		});
		return doc.body.innerHTML;
	}

	/**
	 * 移除包含指定的内容的元素
	 * @param {string} htmlString html文本
	 * @param {string} idPrefix 指定内容
	 * @return {doc.body.innerHTML} 处理好的内容
	 */
	function removeElementsByIdPrefix(html, idPrefix) {
		// 使用 DOMParser 解析 HTML 字符串
		const parser = new DOMParser();
		const doc = parser.parseFromString(html, "text/html");

		// 选择所有 id 属性包含特定前缀的元素
		const elements = doc.querySelectorAll(`[id^="${idPrefix}"]`);

		// 移除这些元素
		elements.forEach((element) => {
			element.remove();
		});

		// 将处理后的 DOM 转回为 HTML 字符串
		return doc.body.innerHTML;
	}

	/**
	 * 创建"快速回复"按钮，用于快速回复本帖内容
	 * @return {HTMLElement} 按钮元素
	 */
	function createFastReplyButton() {
		return createButton("fastReplyButton", "快速回复", function () {
			let fid = getFidFromElement();
			const tid = extractTid(window.location.href);
			showWindow(
				"reply",
				`forum.php?mod=post&action=reply&fid=${fid}&tid=${tid}`
			);
		});
	}

	/**
	 * 创建"查看评分"按钮，用于快速查看本帖评分
	 * @return {HTMLElement} 按钮元素
	 */
	function createViewRatingsButton(pid) {
		return createButton("viewRatingsButton", "查看评分", function () {
			const tid = extractTid(window.location.href);
			showWindow(
				"viewratings",
				`forum.php?mod=misc&action=viewratings&tid=${tid}&pid=${pid}`
			);
		});
	}

	/**
	 * 创建"下载附件"按钮，用于快速下载附件
	 * @return {HTMLElement} 按钮元素
	 */
	function createDownButton() {
		return createButton("downButton", "下载附件", function () {
			// 检查是否已存在模态框
			if (document.getElementById("customModal")) {
				return;
			}

			// 查找所有附件相关元素
			const spans = document.querySelectorAll('span[id*="attach_"]');
			const lockedDivs = Array.from(
				document.querySelectorAll("div.locked")
			).filter((div) => div.textContent.includes("购买"));
			const dls = Array.from(document.querySelectorAll("dl.tattl")).filter(
				(dl) => dl.querySelector("p.attnm")
			);

			// 收集所有附件元素
			const attachmentElements = [];

			// 处理dl.tattl类型的附件 - 这些是已经符合所需格式的附件
			if (dls.length > 0) {
				dls.forEach((dl) => {
					// 克隆元素以避免修改原始DOM
					attachmentElements.push(dl.cloneNode(true));
				});
			}

			// 处理span类型的附件 - 需要转换为dl.tattl格式
			spans.forEach((span) => {
				// 查找附件图标
				let iconSrc = "";
				const prevElement = span.previousElementSibling;
				if (prevElement && prevElement.tagName.toLowerCase() === "img") {
					iconSrc = prevElement.src || prevElement.getAttribute("src") || "";
				}

				// 查找附件链接
				const linkElement = span.querySelector("a");
				if (linkElement) {
					// 创建与示例结构匹配的dl元素
					const dl = document.createElement("dl");
					dl.className = "tattl";

					// 创建dt元素（包含图标）
					const dt = document.createElement("dt");
					if (iconSrc) {
						const img = document.createElement("img");
						img.src = iconSrc;
						img.setAttribute("border", "0");
						img.className = "vm";
						img.alt = "";
						dt.appendChild(img);
					}

					// 创建dd元素（包含附件信息）
					const dd = document.createElement("dd");

					// 创建附件名称段落
					const pName = document.createElement("p");
					pName.className = "attnm";
					pName.appendChild(linkElement.cloneNode(true));
					dd.appendChild(pName);

					// 创建附件信息段落（如大小、下载次数等）
					const spanText = span.textContent.trim();
					if (spanText) {
						const pInfo = document.createElement("p");
						pInfo.textContent = spanText
							.replace(linkElement.textContent, "")
							.trim();
						dd.appendChild(pInfo);
					}

					// 组合所有元素
					dl.appendChild(dt);
					dl.appendChild(dd);
					attachmentElements.push(dl);
				}
			});

			// 处理locked类型的附件
			lockedDivs.forEach((div) => {
				// 创建与示例结构匹配的dl元素
				const dl = document.createElement("dl");
				dl.className = "tattl";

				// 创建dt元素（包含图标）
				const dt = document.createElement("dt");
				const img = document.createElement("img");
				img.src = "https://i.imgur.com/tBQN9h9.png"; // 默认锁定图标
				img.setAttribute("border", "0");
				img.className = "vm";
				img.alt = "";
				dt.appendChild(img);

				// 创建dd元素（包含附件信息）
				const dd = document.createElement("dd");
				dd.innerHTML = div.innerHTML;

				// 组合所有元素
				dl.appendChild(dt);
				dl.appendChild(dd);
				attachmentElements.push(dl);
			});

			if (attachmentElements.length === 0) {
				showTooltip("没有找到任何附件。");
				return;
			}

			// 创建模态框，严格按照指定的HTML结构
			const modal = document.createElement("div");
			modal.id = "customModal";

			// 创建内容容器
			const contentDiv = document.createElement("div");

			// 按照请求的格式添加附件元素
			attachmentElements.forEach((element, index) => {
				contentDiv.appendChild(element);

				// 在每个附件后添加<br>，除了最后一个附件
				if (index < attachmentElements.length - 1) {
					contentDiv.appendChild(document.createElement("br"));
				}
			});

			// 创建关闭按钮
			const closeBtn = document.createElement("button");
			closeBtn.id = "closeModal";
			closeBtn.textContent = "关闭";

			// 按照指定的结构组装模态框
			modal.appendChild(contentDiv);
			modal.appendChild(closeBtn);

			document.body.appendChild(modal);

			// 添加拖动功能到 modal
			enableDrag(modal);

			// 添加关闭按钮事件
			document.getElementById("closeModal").addEventListener("click", () => {
				modal.remove();
			});
		});
	}

	/**
	 * 创建"复制代码"按钮，用于复制页面内所有代码块的内容
	 * @return {HTMLElement} 按钮元素
	 */
	function createCopyCodeButton() {
		return createButton("copyCodeButton", "复制代码", function () {
			let allBlockCodes = document.querySelectorAll(".blockcode");
			let allTexts = [];
			allBlockCodes.forEach((blockCode) => {
				let liElements = blockCode.querySelectorAll("li");
				liElements.forEach((li) => {
					allTexts.push(li.textContent);
				});
			});
			let combinedText = allTexts.join("\n");
			copyToClipboard(
				combinedText,
				() => showTooltip("代码已复制!"),
				(err) => showTooltip("复制失败: ", err)
			);
		});
	}

	/**
	 * 创建"快速评分"按钮，用于页面内对主帖的内容快速评分
	 * @return {HTMLElement} 按钮元素
	 */
	function createQuickGradeButton(pid) {
		return createButton("quickGradeButton", "快速评分", () => grade(pid));
	}

	/**
	 * 创建"快速收藏"按钮，用于页面内对回复的内容快速收藏
	 * @return {HTMLElement} 按钮元素
	 */
	function createQuickStarButton() {
		return createButton("quickStarButton", "快速收藏", star);
	}

	/**
	 * 创建"一键二连"按钮，用于页面内对回复的内容快速评分和收藏
	 * @return {HTMLElement} 按钮元素
	 */
	function createOneClickDoubleButton() {
		return createButton("oneClickDoubleButton", "一键二连", gradeAndStar);
	}

	/**
	 * 创建"快速置顶"按钮，用于页面内对回复的内容快速置顶
	 * @return {HTMLElement} 按钮元素
	 */
	function createQuickTopicadminToPostButton(post, stickreply) {
		var text = stickreply === "1" ? "快速置顶" : "取消置顶";
		return createButton(
			"quickTopicadminToPost",
			text,
			() => {
				let pid = getTableIdFromElement(post);
				if (pid) {
					topicadmin(pid, stickreply);
				} else {
					showTooltip("未找到置顶元素");
				}
			},
			"bgsh-quickTopicadminToPostBtn"
		);
	}
	/**
	 * 创建"快速编辑回复"按钮，用于页面内对回复的内容快速编辑
	 * @return {HTMLElement} 按钮元素
	 */
	function createQuickReplyEditToPostButton(post) {
		var text = "编辑回复";
		return createButton(
			"quickReplyEditToPost",
			text,
			() => {
				let pid = getTableIdFromElement(post);
				if (pid) {
					let fid = getFidFromElement();
					const tid = extractTid(window.location.href);
					window.location.href = `forum.php?mod=post&action=edit&fid=${fid}&tid=${tid}&pid=${pid}`;
				} else {
					showTooltip("未找到回复元素");
				}
			},
			"bgsh-quickReplyEditToPostBtn"
		);
	}

	/**
	 * 创建"快速回复"按钮，用于页面内对回复的内容快速置顶
	 * @return {HTMLElement} 按钮元素
	 */
	function createQuickReplyToPostButton(post) {
		var text = "快速回复";
		return createButton(
			"quickReplyToPost",
			text,
			() => {
				let pid = getTableIdFromElement(post);
				if (pid) {
					let fid = getFidFromElement();
					const tid = extractTid(window.location.href);
					showWindow(
						"reply",
						`forum.php?mod=post&action=reply&fid=${fid}&tid=${tid}&repquote=${pid}`
					);
				} else {
					showTooltip("未找到回复元素");
				}
			},
			"bgsh-quickReplyToPostBtn"
		);
	}

	/**
	 * 创建"广告举报"按钮，用于页面内对回复的内容广告举报
	 * @return {HTMLElement} 按钮元素
	 */
	function createQuickReportadToPostButton(post) {
		var text = "广告举报";
		return createButton(
			"quickReportadToPost",
			text,
			() => {
				let pid = getTableIdFromElement(post);
				if (pid) {
					const tid = extractTid(window.location.href);
					showWindow(
						"reportad",
						`plugin.php?id=pc_reportad&tid=${tid}&pid=${pid}`
					);
				} else {
					showTooltip("未找到举报元素");
				}
			},
			"bgsh-quickReportadToPostBtn"
		);
	}

	/**
	 * 创建"快速举报"按钮，用于页面内对回复的内容快速举报
	 * @return {HTMLElement} 按钮元素
	 */
	function createQuickMiscReportToPostButton(post) {
		var text = "快速举报";
		return createButton(
			"quickMiscReport",
			text,
			() => {
				let pid = getTableIdFromElement(post);
				if (pid) {
					let fid = getFidFromElement();
					const tid = extractTid(window.location.href);
					showWindow(
						`miscreport${pid}`,
						`misc.php?mod=report&rtype=post&rid=${pid}&tid=${tid}&fid=${fid}`
					);
				} else {
					showTooltip("未找到回复元素");
				}
			},
			"bgsh-QuickMiscReportBtn"
		);
	}

	/**
	 * 添加对回复进行操作的按钮，用于页面内对回复的内容快速置顶/回复等等
	 * @return {HTMLElement} 按钮元素
	 */
	function addQuickActionToPostButton() {
		const postContainers = document.querySelectorAll(".po.hin");

		postContainers.forEach((postContainer) => {
			// 检查该元素之后是否已经有一个quickTopicadminToPost
			const existingButton = postContainer.parentNode.querySelector(
				"#quickTopicadminToPost"
			);
			if (existingButton) {
				// 如果已存在按钮，则直接返回
				return;
			}

			// 寻找 element 的父级 tbody
			let parentTbody = postContainer.closest("tbody");
			var stickreply =
				parentTbody &&
				parentTbody.querySelector('img[src="static/image/common/settop.png"]')
					? "0"
					: "1";
			const quickTopicadminToPostButton = createQuickTopicadminToPostButton(
				postContainer,
				stickreply
			);
			const replyToPostButton = createQuickReplyToPostButton(postContainer);
			const quickMiscReportToPostButton =
				createQuickMiscReportToPostButton(postContainer);
			const quickReportadToPostButton =
				createQuickReportadToPostButton(postContainer);
			const setAnswerToPostButton = createSetAnswerToPostButton(postContainer);

			postContainer.appendChild(replyToPostButton);
			postContainer.appendChild(quickTopicadminToPostButton);
			postContainer.appendChild(quickMiscReportToPostButton);
			postContainer.appendChild(quickReportadToPostButton);
			const found = postContainer.querySelector(`.editp`);
			// 如果找到了具有指定类的元素，弹出窗口
			if (found) {
				const quickReplyEditToPostButton =
					createQuickReplyEditToPostButton(postContainer);
				postContainer.appendChild(quickReplyEditToPostButton);
			}
			if (postContainer && postContainer.innerHTML.includes("setanswer(")) {
				postContainer.appendChild(setAnswerToPostButton);
			}
		});
	}

	/**
	 * 用于页面内对头像的屏蔽与显示
	 * @return {HTMLElement} 按钮元素
	 */
	function showAvatarEvent() {
		const avatars = document.querySelectorAll(".avatar");
		const isPostPage = () =>
			/forum\.php\?mod=viewthread|\/thread-\d+-\d+-\d+\.html/.test(
				window.location.href
			);
		if (!isPostPage()) {
			return;
		}
		var settings = getSettings();
		// 遍历所有头像元素
		avatars.forEach((avatar) => {
			// 如果复选框被选中，显示头像；否则，隐藏头像
			if (settings.showAvatar) {
				avatar.style.display = "block";
			} else {
				avatar.style.display = "none";
			}
		});
	}

	// Function to create the toggle button for "displayThreadImages" setting
	function createToggleDisplayThreadImagesButton() {
		let displayThreadImagesEnabled = GM_getValue("displayThreadImages", false); // Default to false as per getSettings

		const button = createButton(
			"toggleDisplayThreadImagesButton", // New ID for clarity
			displayThreadImagesEnabled ? "关闭预览" : "开启预览",
			() => {
				displayThreadImagesEnabled = !displayThreadImagesEnabled;
				GM_setValue("displayThreadImages", displayThreadImagesEnabled);
				button.textContent = displayThreadImagesEnabled ? "关闭预览" : "开启预览";
				showTooltip(displayThreadImagesEnabled ? "预览已开启，页面将刷新应用更改。" : "预览已关闭，页面将刷新应用更改。");

				// 清理无限滚动状态
				if (window.infiniteScrollCleanup) {
					window.infiniteScrollCleanup();
				}

				// Reload the page to apply the change based on 'displayThreadImages'
				setTimeout(() => { // Timeout to allow tooltip to show before reload
					window.location.reload();
				}, 500); // 1.5 seconds delay
			},
			"bgsh-customBtn", // Standard button class
			{ style: "background-color: rgb(3, 150, 255);" } // Consistent styling
		);
		return button;
	}

	/**
	 * 在帖子内容页中添加和执行各种功能
	 * @param {Object} settings - 用户的设置
	 * @param {HTMLElement} buttonContainer - 按钮容器
	 */
	function handlePostPage(settings, buttonContainer) {
		// Removed call to applyImagePreviewState as it's being refactored

		const toggleImages = (action) => {
			const images = document.querySelectorAll("img.zoom");
			images.forEach(
				(img) => (img.style.display = action === "hide" ? "none" : "")
			);
		};

		toggleImages(settings.showImageButton);

		// 只有当showToggleImageButton为true时才添加隐藏/显示图片按钮
		if (settings.showToggleImageButton) {
			const initialButtonText =
				settings.showImageButton === "show" ? "隐藏图片" : "显示图片";

			const toggleButton = createButton(
				"toggleImageDisplay",
				initialButtonText,
				function () {
					if (toggleButton.innerText === "显示图片") {
						toggleImages("show");
						toggleButton.innerText = "隐藏图片";
						GM_setValue("showImageButton", "show");
					} else {
						toggleImages("hide");
						toggleButton.innerText = "显示图片";
						GM_setValue("showImageButton", "hide");
					}
				}
			);
			buttonContainer.appendChild(toggleButton);
		}

		if (settings.showDown) {
			buttonContainer.appendChild(createDownButton());
		}

		let codeBlocks = document.querySelectorAll(".blockcode");
		if (codeBlocks.length > 0 && settings.showCopyCode) {
			buttonContainer.appendChild(createCopyCodeButton());
		}
		let firstPobClElement = document.querySelector(".po.hin");
		let pid = getTableIdFromElement(firstPobClElement);

		const userid = getUserId();
		if (userid) {
			if (settings.showFastPost) {
				buttonContainer.appendChild(createFastPostButton());
			}
			if (settings.showFastReply) {
				buttonContainer.appendChild(createFastReplyButton());
			}
			if (settings.showQuickGrade) {
				buttonContainer.appendChild(createQuickGradeButton(pid));
			}
			if (settings.showQuickStar) {
				buttonContainer.appendChild(createQuickStarButton());
			}
			if (settings.showClickDouble) {
				buttonContainer.appendChild(createOneClickDoubleButton());
			}

			// addQuickGradeToPostButton();//已失效和谐
			addQuickActionToPostButton();
		}
		if (settings.showViewRatings) {
			buttonContainer.appendChild(createViewRatingsButton(pid));
		}
		if (settings.showFastCopy) {
			buttonContainer.appendChild(createFastCopyButton());
		}

		if (settings.defaultSwipeToSearch) {
			document.addEventListener("mouseup", selectSearch);
		}
		initInfiniteScroll("isPostPage");
		showAvatarEvent();
		removeAD3();
		replacePMonPost();
		removeFastReply();
	}

	/**
	 * 移除帖子内容页广告
	 */
	async function removeAD3() {
		document.querySelectorAll('[class*="show-text"]').forEach((element) => {
			element.remove();
		});
		document.querySelectorAll('[id*="mgc_post"]').forEach((element) => {
			element.remove();
		});
		document.querySelectorAll("#p_btn").forEach((element) => {
			element.remove();
		});
		document.querySelectorAll(".pob.cl").forEach((element) => {
			element.remove();
		});
	}

	/**
	 * 移除帖子底部的快速回帖
	 */
	function removeFastReply() {
		document.querySelectorAll("#f_pst").forEach((element) => {
			element.remove();
		});
	}
	/**
	 * 替换帖子内容页私信
	 */
	async function replacePMonPost() {
		let firstPobClElement = document.querySelector(".po.hin");
		let pid = getTableIdFromElement(firstPobClElement);
		document.querySelectorAll('[class*="pm2"]').forEach((element) => {
			// 获取element内部的<a>标签
			const anchor = element.querySelector("a");
			if (anchor) {
				// 解析<a>标签的href属性以获取touid
				const href = anchor.getAttribute("href");
				const urlParams = new URLSearchParams(href);
				const touid = urlParams.get("touid");

				// 确保touid存在
				if (touid) {
					// 创建新的按钮，假设createFastPMButton接受touid作为参数
					const newButton = createFastPMButton(pid, touid);

					// 插入按钮
					if (element.nextSibling) {
						element.parentNode.insertBefore(newButton, element.nextSibling);
					} else {
						element.parentNode.appendChild(newButton);
					}
				}
			}

			// 移除原始元素
			element.remove();
		});
	}

	// #endregion

	// #region iframe模态框功能

	/**
	 * 在iframe中添加右侧快捷按钮
	 * @param {Document} iframeDoc - iframe的document对象
	 * @param {Window} iframeWindow - iframe的window对象
	 */
	function addIframeQuickButtons(iframeDoc, iframeWindow) {
		// 在iframe模态框中不显示任何右侧按钮，提供更简洁的发帖体验
		console.log('iframe模态框中不显示右侧按钮');
		return;
	}



	// #endregion

	// #region 网站全局功能

	/**
	 * 综合区快速发帖
	 */
	function PostContent() {
		if (!window.location.href.includes("forum.php?mod=post&action=newthread")) {
			return;
		}
		const link = document.createElement("a");
		link.href =
			"/forum.php?mod=redirect&goto=findpost&ptid=1708826&pid=16039784";
		link.textContent = "发帖须知";
		link.target = "_blank";
		const organizeButton = document.createElement("li");
		organizeButton.className = "a";
		organizeButton.innerHTML =
			'<button id="organizeBtn" type="button">整理</button>';

		const shareButton = document.createElement("li");
		shareButton.className = "a";
		shareButton.innerHTML = '<button id="shareBtn" type="button">自转</button>';
		const ulElement = document.querySelector(".tb.cl.mbw");
		if (ulElement) {
			ulElement.appendChild(link);
			ulElement.appendChild(organizeButton);
			ulElement.appendChild(shareButton);
		} else {
			console.warn("未找到指定的ul元素");
			return;
		}
		var ttttype = "";
		const modalContent = `
  <div id="organizeModal">
    <!-- 关闭按钮 -->
<button class="close-btn">
  <span class="close-btn-line"></span>
  <span class="close-btn-line"></span>
</button>

    <div>
      <strong>【资源名称】：</strong>
      <input type="text" id="resourceName"/>
    </div>
    <div>
      <strong>【资源类型】：</strong>
      <label><input type="radio" name="resourceType" value="影片" checked />影片</label>
      <label><input type="radio" name="resourceType" value="视频" />视频</label>
      <label><input type="radio" name="resourceType" value="动漫" />动漫</label>
      <label><input type="radio" name="resourceType" value="套图" />套图</label>
      <label><input type="radio" name="resourceType" value="游戏" />游戏</label>
    </div>
    <div>
      <strong>【是否有码】：</strong>
      <label><input type="radio" name="censorship" value="有码" checked />有码</label>
      <label><input type="radio" name="censorship" value="无码" />无码</label>
    </div>
    <div>
      <strong>【是否水印】：</strong>
      <label><input type="radio" name="watermark" value="有水印" />有水印</label>
      <label><input type="radio" name="watermark" value="无水印" checked />无水印</label>
    </div>
    <div>
      <strong>【字幕选项】：</strong>
      <label><input type="radio" name="subtitle" value="中文字幕" />中文字幕</label>
      <label><input type="radio" name="subtitle" value="日文字幕" />日文字幕</label>
      <label><input type="radio" name="subtitle" value="英文字幕" />英文字幕</label>
      <label><input type="radio" name="subtitle" value="无字幕" checked />无字幕</label>
    </div>
    <div>
      <strong>【资源大小】：</strong>
      <input type="text" id="resourceSize" placeholder="""/>
      <label><input type="radio" name="sizeUnit" value="M" />M</label>
      <label><input type="radio" name="sizeUnit" value="G" checked />G</label>
      <label><input type="radio" name="sizeUnit" value="T" />T</label>
    </div>
    <div>
      <strong>【下载类型】：</strong>
      <label><input type="radio" name="downType" value="115ED2K" checked />115ED2K</label>
      <label><input type="radio" name="downType" value="BT/磁链" />BT/磁链</label>
      <label><input type="radio" name="downType" value="ED2K" />ED2K</label>
      <label><input type="radio" name="downType" value="夸克网盘" />夸克网盘</label>
      <label><input type="radio" name="downType" value="百度网盘" />百度网盘</label>
      <label><input type="radio" name="downType" value="PikPak网盘" />PikPak网盘</label>
      <label><input type="radio" name="downType" value="其它网盘" />其它网盘</label>
    </div>
    【视频数量】：<input type="text" id="videoCount" placeholder="""/><br>
    【图片数量】：<input type="text" id="imageCount" placeholder="""/><br>
    【配额数量】：<input type="text" id="quota" placeholder="""/>
    <div><strong>【资源预览】：</strong></div>
    <div><strong>【资源链接】：</strong><input type="text" id="resourceLink""/></div>
    <button id="insetBtn" type="button">插入</button>
  </div>`;
		document.body.insertAdjacentHTML("beforeend", modalContent);

		// 为organizeModal添加拖动功能
		const organizeModalElement = document.getElementById("organizeModal");
		if (organizeModalElement) {
			// 修改样式以使拖动生效
			organizeModalElement.style.position = "fixed";
			organizeModalElement.style.cursor = "move";
			enableDrag(organizeModalElement);
		}

		document
			.getElementById("organizeBtn")
			.addEventListener("click", function () {
				showModal("整理");
			});
		document.getElementById("shareBtn").addEventListener("click", function () {
			showModal("自转");
		});
		function showModal(param) {
			console.log(param);
			ttttype = param;
			const modal = document.getElementById("organizeModal");
			// 显示模态框
			modal.style.display = "block";
			// 重置位置，避免之前的拖动影响
			if (!modal.dataset.hasBeenDragged) {
				modal.style.left = "50%";
				modal.style.top = "50%";
				modal.style.transform = "translate(-50%, -50%)";
			} else {
				// 如果已经被拖动过，保持当前位置
				modal.style.transform = "none";
			}
		}

		// 为关闭按钮添加点击事件
		document
			.querySelector("#organizeModal .close-btn")
			.addEventListener("click", function () {
				document.getElementById("organizeModal").style.display = "none";
			});

		document.getElementById("insetBtn").addEventListener("click", function () {
			const resourceName = document.getElementById("resourceName").value;
			const resourceType = document.querySelector(
				'input[name="resourceType"]:checked'
			)?.value;
			const censorship = document.querySelector(
				'input[name="censorship"]:checked'
			)?.value;
			const watermark = document.querySelector(
				'input[name="watermark"]:checked'
			)?.value;
			const subtitle = document.querySelector(
				'input[name="subtitle"]:checked'
			)?.value;
			const resourceLink = document.getElementById("resourceLink").value;
			const downType = document.querySelector(
				'input[name="downType"]:checked'
			)?.value;
			const resourceSize = document.getElementById("resourceSize").value;
			const sizeUnit = document.querySelector(
				'input[name="sizeUnit"]:checked'
			).value;
			const videoCount = document.getElementById("videoCount").value;
			const imageCount = document.getElementById("imageCount").value;
			const quota = document.getElementById("quota").value;
			let resourceSizeStr = resourceSize ? `${resourceSize}${sizeUnit}` : "";
			let videoCountStr = videoCount ? `${videoCount}V` : "";
			let imageCountStr = imageCount ? `${imageCount}P` : "";
			let quotaStr = quota ? `${quota}配额` : "";
			const content = `
              【资源名称】：${resourceName}<br>
              【资源类型】：${resourceType}<br>
              【是否有码】：${censorship} @ ${watermark} @ ${subtitle}<br>
              【资源大小】：${resourceSizeStr}/${videoCountStr}/${imageCountStr}/${quotaStr}<br>
              【资源预览】：<br>
              【资源链接】：<div class="blockcode"><blockquote>${resourceLink}</blockquote></div><br>
          `;
			const iframe = document.querySelector(".area iframe");
			if (iframe && iframe.contentDocument) {
				const body = iframe.contentDocument.body;
				if (body && body.isContentEditable) {
					body.innerHTML = content;
				} else {
					console.warn("在iframe中未找到可编辑的body元素");
				}
			} else {
				console.warn("未找到class为area的div中的iframe");
			}
			const title = `【${ttttype}】【${downType}】${resourceName}【${resourceSizeStr}/${videoCountStr}/${imageCountStr}/${quotaStr}】
          `;
			const subjectInput = document.getElementById("subject");
			if (subjectInput) {
				subjectInput.value = title;
			} else {
				console.warn("未找到ID为'subject'的input元素");
			}
			var selectElement = document.getElementById("typeid");
			if (selectElement) {
				selectElement.setAttribute("selecti", "8");
			}
			var aElement = document.querySelector(".ftid a#typeid_ctrl");
			if (aElement) {
				aElement.textContent = "情色分享";
				aElement.setAttribute("initialized", "true");
			}
			document.getElementById("organizeModal").style.display = "none";
		});
	}

	/**
	 * 监听新布局的变化并应用屏蔽功能
	 * @param {Object} settings - 用户的设置
	 */
	function observeRedesignedLayout(settings) {
		// 如果不是新布局，直接返回
		if (!isRedesignedLayout()) {
			return;
		}

		// 防抖函数，避免频繁执行
		let debounceTimer = null;
		const debounceDelay = 300;

		// 创建一个观察器来监听新卡片的添加
		const observer = new MutationObserver((mutations) => {
			let hasNewCards = false;
			let hasNewContent = false;

			mutations.forEach((mutation) => {
				if (mutation.type === 'childList') {
					mutation.addedNodes.forEach((node) => {
						if (node.nodeType === Node.ELEMENT_NODE) {
							// 检查是否添加了新的卡片
							if (node.classList && node.classList.contains('thread-card')) {
								hasNewCards = true;
							} else if (node.querySelector && node.querySelector('.thread-card')) {
								hasNewCards = true;
							}
							// 检查是否有其他新内容
							hasNewContent = true;
						}
					});
				}
			});

			// 如果有新卡片或新内容，应用屏蔽功能
			if (hasNewCards || hasNewContent) {
				// 清除之前的定时器
				if (debounceTimer) {
					clearTimeout(debounceTimer);
				}

				// 设置新的定时器
				debounceTimer = setTimeout(() => {
					// 重新执行所有屏蔽功能，确保新内容被处理
					blockContentInRedesignedLayout(settings);
					blockContentByTitleInRedesignedLayout(settings);
					blockingResolvedActionInRedesignedLayout(settings);

					if (settings.displayThreadImages) {
						displayThreadImagesInRedesignedLayout(settings);
					}

					debounceTimer = null;
				}, debounceDelay);
			}
		});

		// 开始观察新布局容器
		const redesignedList = document.querySelector('.redesigned-thread-list');
		if (redesignedList) {
			observer.observe(redesignedList, {
				childList: true,
				subtree: true
			});
		}

		// 同时观察整个文档，以防遗漏
		const documentObserver = new MutationObserver((mutations) => {
			let needsRecheck = false;

			mutations.forEach((mutation) => {
				if (mutation.type === 'childList') {
					mutation.addedNodes.forEach((node) => {
						if (node.nodeType === Node.ELEMENT_NODE) {
							// 检查是否添加了包含thread-card的内容
							if (node.querySelector && node.querySelector('.thread-card')) {
								needsRecheck = true;
							}
						}
					});
				}
			});

			if (needsRecheck) {
				// 清除之前的定时器
				if (debounceTimer) {
					clearTimeout(debounceTimer);
				}

				// 设置新的定时器
				debounceTimer = setTimeout(() => {
					blockContentByTitleInRedesignedLayout(settings);
					debounceTimer = null;
				}, debounceDelay);
			}
		});

		// 观察整个文档
		documentObserver.observe(document.body, {
			childList: true,
			subtree: true
		});

		// 定期检查功能，确保不遗漏任何内容
		setInterval(() => {
			const uncheckedCards = document.querySelectorAll('.thread-card:not([data-keyword-checked])');
			if (uncheckedCards.length > 0) {
				blockContentByTitleInRedesignedLayout(settings);
			}
		}, 3000); // 每3秒检查一次

		// 强制重新检查所有卡片（忽略标记）
		setInterval(() => {
			forceRecheckAllCards(settings);
		}, 10000); // 每10秒强制检查一次
	}

	/**
	 * 强制重新检查所有卡片，忽略已检查标记
	 * @param {Object} settings - 用户设置
	 */
	function forceRecheckAllCards(settings) {
		const { excludePostOptions, displayBlockedTips } = settings;

		if (!excludePostOptions || excludePostOptions.length === 0) {
			return;
		}

		const allCards = document.querySelectorAll('.thread-card');

		allCards.forEach((card) => {
			// 跳过已经被屏蔽的卡片
			if (card.style.display === 'none' || card.innerHTML.includes('已屏蔽主题关键词')) {
				return;
			}

			const titleElement = card.querySelector('.thread-title a');
			if (titleElement) {
				// 尝试多种方式获取完整标题
				let title = titleElement.textContent.trim();

				// 如果标题太短，尝试获取innerHTML并清理HTML标签
				if (title.length < 10) {
					const innerHTML = titleElement.innerHTML;
					title = innerHTML.replace(/<[^>]*>/g, '').trim();
				}

				// 如果还是太短，尝试从父元素获取
				if (title.length < 10) {
					const parentTitle = card.querySelector('.thread-title');
					if (parentTitle) {
						title = parentTitle.textContent.trim();
					}
				}

				// 详细检查每个关键字
				let matchedKeyword = null;
				for (let i = 0; i < excludePostOptions.length; i++) {
					const keyword = excludePostOptions[i];
					if (title.includes(keyword)) {
						matchedKeyword = keyword;
						break;
					}
				}

				if (matchedKeyword) {
					if (displayBlockedTips) {
						card.innerHTML = `
							<div class="thread-content" style="padding: 20px; text-align: center; color: #666;">
								<div class="thread-title">
									<b>已屏蔽主题关键词: ${matchedKeyword}</b>
								</div>
								<div style="margin-top: 8px; font-size: 12px; color: #999;">
									原标题包含屏蔽关键词（强制检查发现）
								</div>
							</div>
						`;
						card.style.background = 'rgba(200, 200, 200, 0.3)';
					} else {
						forceHideCard(card);
					}

					// 标记为已检查
					card.setAttribute('data-keyword-checked', 'true');
				}
			}
		});
	}

	/**
	 * 全站通用的入口方法。为整个站点执行基本操作和应用用户设置。
	 *
	 * 1. 修改用户勋章显示
	 * 2. 添加自定义样式
	 * 3. 根据当前页面的URL，选择并执行相应的页面处理逻辑
	 * 4. 如果用户登录，尝试执行自动签到操作
	 * 5. 将按钮容器附加到页面主体
	 *
	 * @param {Object} settings - 用户的设置
	 */
	function baseFunction(settings) {
		removeAD();
		if (settings.blockingIndex) {
			removeIndex();
		}
		manipulateMedals(settings); // 修改用户勋章显示
		addStyles(); // 添加自定义样式
		const buttonContainer = createButtonContainer();
		delegatePageHandlers(settings, buttonContainer); // 根据URL选择页面处理逻辑

		// 添加"开启预览"按钮（在签到按钮之前）
		addToggleImagePreviewButton(buttonContainer);

		handleUserSign(buttonContainer); // 执行用户签到逻辑

		// 监听discuz_redesign.js的完成事件
		document.addEventListener('discuzRedesignComplete', function() {
			// 延迟一点时间确保布局完全稳定
			setTimeout(() => {
				if (settings.displayThreadImages) {
					displayThreadImages(settings);
				}
			}, 200);
		});

		// 等待一段时间后再执行屏蔽和观察器，确保discuz_redesign.js已经执行完毕
		// 使用多次检查确保新布局完全加载
		const executeBlockingWithRetry = (retryCount = 0, maxRetries = 10) => {
			// 检查是否是论坛列表页面
			const isForumDisplayPage = /forum\.php\?mod=forumdisplay|\/forum-\d+-\d+\.html/.test(window.location.href);

			if (isForumDisplayPage) {
				const redesignedContainer = document.querySelector('.redesigned-thread-list');
				const threadCards = document.querySelectorAll('.thread-card');

				// 如果存在新布局且有卡片，或者重试次数已达上限，则执行屏蔽
				if ((redesignedContainer && threadCards.length > 0) || retryCount >= maxRetries) {
					blockContentByUsers(settings); //屏蔽用户
					blockContentByTitle(settings); //屏蔽关键字
					observeRedesignedLayout(settings);

					// 延迟执行图片预览功能，确保新布局完全稳定
					setTimeout(() => {
						displayThreadImages(settings);
					}, 300);
				} else if (retryCount < maxRetries) {
					// 如果新布局还没准备好，继续重试
					setTimeout(() => executeBlockingWithRetry(retryCount + 1, maxRetries), 100);
				}
			} else {
				// 非论坛列表页面，直接执行
				blockContentByUsers(settings); //屏蔽用户
				blockContentByTitle(settings); //屏蔽关键字
				observeRedesignedLayout(settings);

				// 非论坛列表页面也执行图片预览（如果适用）
				displayThreadImages(settings);
			}
		};

		setTimeout(() => executeBlockingWithRetry(), 200);

		createBaoguoButton(buttonContainer);
		document.body.appendChild(buttonContainer); // 将按钮容器附加到页面主体
		PostContent();
	}

	/**
	 * 检查当前页面的URL是否匹配SpacePage的模式。
	 * @returns {boolean} 如果匹配则返回true，否则返回false。
	 */
	function delegatePageHandlers(settings, buttonContainer) {
		const isPostPage = () =>
			/forum\.php\?mod=viewthread|\/thread-\d+-\d+-\d+\.html/.test(
				window.location.href
			);
		const isSearchPage = () =>
			/search\.php\?mod=forum/.test(window.location.href);
		const isForumDisplayPage = () =>
			/forum\.php\?mod=forumdisplay|\/forum-\d+-\d+\.html/.test(
				window.location.href
			);
		const isSpacePage = () =>
			/home\.php\?mod=space&uid=\d+&do=thread&view=me&from=space(.*type=reply)?/.test(
				window.location.href
			);

		if (isPostPage()) {
			handlePostPage(settings, buttonContainer);
		} else if (isSearchPage()) {
			handleSearchPage(settings);
		} else if (isForumDisplayPage()) {
			handleForumDisplayPage(settings, buttonContainer);
		} else if (isSpacePage()) {
			initInfiniteScroll("isSpacePage");
		}
	}

	/**
	 * 添加"开启预览"按钮
	 * @param {Element} buttonContainer - 按钮容器元素
	 */
	function addToggleImagePreviewButton(buttonContainer) {
		// 检查是否为发帖页面
		const isNewThreadPage = window.location.href.includes("forum.php?mod=post&action=newthread");

		// 只有在非发帖页面才添加"开启预览"按钮
		if (!isNewThreadPage) {
			const toggleImagePreviewBtn = createToggleDisplayThreadImagesButton(); // Use the new function name
			buttonContainer.appendChild(toggleImagePreviewBtn);
		}
	}

	/**
	 * 创建设置按钮
	 * @param {Object} settings - 用户的设置
	 * @param {Element} buttonContainer - 按钮容器元素
	 */
	function createBaoguoButton(buttonContainer) {
		var baoguoButton = createButton("baoguoButton", "功能设置", () =>
			createSettingsUI(getSettings())
		);
		buttonContainer.appendChild(baoguoButton);
	}

	/**
	 * 用户签到处理逻辑。检查用户是否已签到并执行相应操作。
	 *
	 * 1. 获取用户ID。如果用户未登录，则不执行任何操作。
	 * 2. 检查用户今天是否已签到。
	 * 3. 根据签到状态，更新签到按钮的文本。
	 * 4. 如果用户今天还未签到，尝试自动签到。
	 * 5. 将签到按钮添加到指定的按钮容器。
	 *
	 * @param {HTMLElement} buttonContainer - 存放按钮的容器元素
	 */
	async function handleUserSign(buttonContainer) {
		const userid = getUserId(); // 获取用户ID
		if (!userid) return; // 如果用户未登录，结束函数

		// 检查今天是否已经签到
		const lastSignDate = GM_getValue(`lastSignDate_${userid}`, null);
		const today = new Date().toLocaleDateString();
		const hasSignedToday = lastSignDate === today;

		// 更新签到按钮文本
		const signButtonText = hasSignedToday ? "已经签到" : "快去签到";
		const signButton = createButton(
			"signButton",
			signButtonText,
			() => (window.location.href = `${baseURL}/plugin.php?id=dd_sign:index`)
		);

		// 尝试自动签到
		if (!hasSignedToday) {
			const signed = await sign(userid);
			signButton.innerText = signed ? "已经签到" : "快去签到";
		}

		// 添加签到按钮到容器
		buttonContainer.appendChild(signButton);
	}

	/**
	 * 移除广告
	 */
	async function removeAD() {
		document.querySelectorAll(".show-text.cl").forEach((element) => {
			element.remove();
		});
		const qmenuelement = document.querySelector("#qmenu");
		if (qmenuelement) {
			qmenuelement.remove();
		}
	}

	/**
	 * 移除首页热门
	 */
	async function removeIndex() {
		const diy_chart = document.querySelector("#diy_chart");
		if (diy_chart) {
			diy_chart.remove();
		}
	}

	// #endregion

	// #region 持久性设置

	/**
	 * 保存用户的设置并执行相应的操作。
	 *
	 * 1. 获取当前保存的设置。
	 * 2. 从页面的UI元素中读取新的设置值。
	 * 3. 对某些设置值进行额外处理。
	 * 4. 创建一个需要保存的设置对象。
	 * 5. 使用GM_setValue存储设置。
	 * 6. 根据新的设置值应用更改。
	 * 7. 如果某些核心设置已更改，重新加载页面。
	 *
	 * @param {Object} settings - 用户的设置对象
	 */
	function saveSettings(settings) {
		const oldSettings = getSettings();

		try {
			// 定义一个辅助函数来安全地获取DOM元素值
			const getElementValue = (id, defaultValue, type = "value") => {
				const element = document.getElementById(id);
				if (!element) {
					console.error(`元素 ${id} 未找到`);
					return defaultValue;
				}
				return type === "value" ? element.value : element.checked;
			};

			// 使用辅助函数读取所有设置值
			settings.logoText = getElementValue("logoTextInput", settings.logoText);
			settings.tipsText = getElementValue("tipsTextInput", settings.tipsText);
			settings.showDown = getElementValue(
				"showDownCheckbox",
				settings.showDown,
				"checked"
			);
			settings.showCopyCode = getElementValue(
				"showCopyCodeCheckbox",
				settings.showCopyCode,
				"checked"
			);
			settings.showFastPost = getElementValue(
				"showFastPostCheckbox",
				settings.showFastPost,
				"checked"
			);
			settings.showFastReply = getElementValue(
				"showFastReplyCheckbox",
				settings.showFastReply,
				"checked"
			);
			settings.showQuickGrade = getElementValue(
				"showQuickGradeCheckbox",
				settings.showQuickGrade,
				"checked"
			);
			settings.showQuickStar = getElementValue(
				"showQuickStarCheckbox",
				settings.showQuickStar,
				"checked"
			);
			settings.showClickDouble = getElementValue(
				"showClickDoubleCheckbox",
				settings.showClickDouble,
				"checked"
			);
			settings.showViewRatings = getElementValue(
				"showViewRatingsCheckbox",
				settings.showViewRatings,
				"checked"
			);
			settings.showFastCopy = getElementValue(
				"showFastCopyCheckbox",
				settings.showFastCopy,
				"checked"
			);
			settings.blockingIndex = getElementValue(
				"blockingIndexCheckbox",
				settings.blockingIndex,
				"checked"
			);
			settings.displayBlockedTips = getElementValue(
				"displayBlockedTipsCheckbox",
				settings.displayBlockedTips,
				"checked"
			);
			settings.maxGradeThread = getElementValue(
				"maxGradeThread",
				settings.maxGradeThread
			);
			settings.showAvatar = getElementValue(
				"showAvatarCheckbox",
				settings.showAvatar,
				"checked"
			);
			settings.showToggleImageButton = getElementValue(
				"showToggleImageButtonCheckbox",
				settings.showToggleImageButton,
				"checked"
			);

			// 处理blockedUsers列表
			const blockedUsersList = document.getElementById("blockedUsersList");
			if (blockedUsersList) {
				settings.blockedUsers = blockedUsersList.value
					.split("\n")
					.map((name) => name.trim())
					.filter((user) => user.trim() !== "");
			} else {
				console.error("blockedUsersList元素未找到");
			}

			// 处理其他需要特殊处理的元素
			const enableTitleStyleCheckbox = document.getElementById(
				"enableTitleStyleCheckbox"
			);
			if (enableTitleStyleCheckbox) {
				settings.enableTitleStyle = enableTitleStyleCheckbox.checked;
			} else {
				console.error("enableTitleStyleCheckbox元素未找到");
			}

			const autoPaginationCheckbox = document.getElementById(
				"autoPaginationCheckbox"
			);
			if (autoPaginationCheckbox) {
				settings.autoPagination = autoPaginationCheckbox.checked;
			} else {
				console.error("autoPaginationCheckbox元素未找到");
			}

			const displayThreadImagesCheckbox = document.getElementById(
				"displayThreadImagesCheckbox"
			);
			if (displayThreadImagesCheckbox) {
				settings.displayThreadImages = displayThreadImagesCheckbox.checked;
			} else {
				console.error("displayThreadImagesCheckbox元素未找到");
			}

			// 处理blockMedals单选框
			settings.blockMedals = getCheckedRadioValue("blockMedals");

			// 处理excludeOptions和excludePostOptions文本区域
			const excludeOptionsTextarea = document.getElementById(
				"excludeOptionsTextarea"
			);
			if (excludeOptionsTextarea) {
				settings.excludeOptions = [
					...new Set(
						excludeOptionsTextarea.value
							.split("\n")
							.map((line) => line.trim())
							.filter((line) => line !== "")
					),
				];
			} else {
				console.error("excludeOptionsTextarea元素未找到");
			}

			const excludePostOptionsTextarea = document.getElementById(
				"excludePostOptionsTextarea"
			);
			if (excludePostOptionsTextarea) {
				settings.excludePostOptions = [
					...new Set(
						excludePostOptionsTextarea.value
							.split("\n")
							.map((line) => line.trim())
							.filter((line) => line !== "")
					),
				];
			} else {
				console.error("excludePostOptionsTextarea元素未找到");
			}

			// 过滤excludeGroup
			settings.excludeGroup = settings.excludeGroup.filter((group) =>
				settings.excludeOptions.includes(group)
			);

			// 创建要保存的设置对象
			const settingsToSave = {
				logoText: settings.logoText,
				tipsText: settings.tipsText,
				blockMedals: settings.blockMedals,
				displayBlockedTips: settings.displayBlockedTips,
				blockedUsers: settings.blockedUsers,
				enableTitleStyle: settings.enableTitleStyle,
				excludeOptions: settings.excludeOptions,
				excludeGroup: settings.excludeGroup,
				autoPagination: settings.autoPagination,
				showAvatar: settings.showAvatar,
				excludePostOptions: settings.excludePostOptions,
				displayThreadImages: settings.displayThreadImages,
				maxGradeThread: settings.maxGradeThread,
				showDown: settings.showDown,
				showCopyCode: settings.showCopyCode,
				showFastPost: settings.showFastPost,
				showFastReply: settings.showFastReply,
				showQuickGrade: settings.showQuickGrade,
				showQuickStar: settings.showQuickStar,
				showClickDouble: settings.showClickDouble,
				showViewRatings: settings.showViewRatings,
				showToggleImageButton: settings.showToggleImageButton,
				showFastCopy: settings.showFastCopy,
				blockingIndex: settings.blockingIndex,
			};

			// 存储设置
			for (let key in settingsToSave) {
				GM_setValue(key, settingsToSave[key]);
			}

			// 应用更改
			try {
				if (typeof manipulateMedals === "function") {
					manipulateMedals(settings);
				}

				// stylePosts 和 undoStylePosts 函数已移除
				// 这些函数用于原始布局的标题样式，现在已不再需要
				if (settings.enableTitleStyle) {
					console.log('标题样式功能已移除，因为discuz_redesign.js已经改变了页面结构');
				}

				if (typeof showAvatarEvent === "function") {
					showAvatarEvent();
				}
			} catch (e) {
				console.error("应用设置更改时出错:", e);
			}

			// 检查是否需要重新加载页面
			if (
				oldSettings.blockingIndex !== settings.blockingIndex ||
				oldSettings.showFastCopy !== settings.showFastCopy ||
				oldSettings.showViewRatings !== settings.showViewRatings ||
				oldSettings.showToggleImageButton !== settings.showToggleImageButton ||
				oldSettings.showClickDouble !== settings.showClickDouble ||
				oldSettings.showQuickStar !== settings.showQuickStar ||
				oldSettings.showQuickGrade !== settings.showQuickGrade ||
				oldSettings.showFastReply !== settings.showFastReply ||
				oldSettings.showFastPost !== settings.showFastPost ||
				oldSettings.showCopyCode !== settings.showCopyCode ||
				oldSettings.showDown !== settings.showDown ||
				oldSettings.displayBlockedTips !== settings.displayBlockedTips ||
				oldSettings.displayThreadImages !== settings.displayThreadImages ||
				oldSettings.autoPagination !== settings.autoPagination ||
				oldSettings.blockMedals !== settings.blockMedals ||
				oldSettings.blockedUsers.toString() !==
					settings.blockedUsers.toString() ||
				oldSettings.excludeOptions.toString() !==
					settings.excludeOptions.toString() ||
				oldSettings.excludePostOptions.toString() !==
					settings.excludePostOptions.toString()
			) {
				console.log("设置已更改，页面将重新加载");
				location.reload();
			} else {
				console.log("设置已保存，无需重新加载页面");
			}
		} catch (error) {
			console.error("保存设置时出错:", error);
			alert("保存设置时出错，请查看控制台以获取详细信息");
		}
	}
	// #endregion

	// #region 高级搜索

	/**
	 * 在页面上添加高级搜索功能。
	 *
	 * 1. 检查页面中是否存在目标元素。
	 * 2. 创建一个高级搜索区域并将其附加到页面。
	 * 3. 根据传入的设置初始化复选框的状态。
	 * 4. 为高级搜索区域的复选框组添加事件监听器。
	 *
	 * @param {Object} settings - 用户的设置对象
	 */
	function addAdvancedSearch(settings) {
		const tlElement = document.querySelector(".tl");
		if (!tlElement) {
			console.error("The .tl element not found!");
			return;
		}

		const advancedSearchDiv = createAdvancedSearchDiv(settings);
		document.body.appendChild(advancedSearchDiv);

		initCheckboxGroupWithSettings(advancedSearchDiv, settings);
		addEventListenerForAdvancedSearch(advancedSearchDiv);
	}

	/**
	 * 创建一个高级搜索区域（div）。
	 * 区域中包含复选框组，允许用户选择不同的搜索选项。
	 *
	 * @param {Object} settings - 用户的设置对象
	 * @param {Array} TIDOptions - 板块选项，默认值为DEFAULT_TID_OPTIONS
	 * @returns {HTMLElement} - 创建的div元素
	 */
	function createAdvancedSearchDiv(settings, TIDOptions = DEFAULT_TID_OPTIONS) {
		const advancedSearchDiv = document.createElement("div");
		const excludeOptions = settings.excludeOptions || [];
		const excludeOptionsFormatted = excludeOptions.map((option) => ({
			label: option,
			value: option,
		}));

		advancedSearchDiv.appendChild(
			createCheckboxGroup("excludeGroup", "排除关键字", excludeOptionsFormatted)
		);
		advancedSearchDiv.appendChild(
			createCheckboxGroup("TIDGroup", "只看板块", TIDOptions)
		);

		// 添加样式类
		advancedSearchDiv.classList.add("advanced-search");

		return advancedSearchDiv;
	}

	/**
	 * 根据传入的设置初始化复选框的状态。
	 *
	 * @param {HTMLElement} div - 包含复选框组的div元素
	 * @param {Object} settings - 用户的设置对象
	 */
	function initCheckboxGroupWithSettings(div, settings) {
		const setCheckboxes = (group, values) => {
			values.forEach((value) => {
				const checkbox = div.querySelector(`#${group} input[value="${value}"]`);
				if (checkbox) checkbox.checked = true;
			});
		};

		setCheckboxes("excludeGroup", settings.excludeGroup);
		setCheckboxes("TIDGroup", settings.TIDGroup);
	}

	/**
	 * 为高级搜索区域的复选框组添加事件监听器。
	 * 当用户更改复选框的状态时，会更新和保存设置。
	 *
	 * @param {HTMLElement} div - 包含复选框组的div元素
	 */
	function addEventListenerForAdvancedSearch(div) {
		div.addEventListener("change", function (e) {
			const handleCheckboxChange = (group) => {
				if (e.target.closest(`#${group}`)) {
					const selectedValues = [
						...document.querySelectorAll(`#${group} input:checked`),
					].map((input) => input.value);
					GM_setValue(group, JSON.stringify(selectedValues));
				}
			};

			handleCheckboxChange("excludeGroup");
			handleCheckboxChange("TIDGroup");
			filterElementsBasedOnSettings(getSettings());
		});
	}

	// #endregion

	// 添加消息监听器处理iframe中的事件
	window.addEventListener('message', (event) => {
		if (event.data && event.data.type === 'openSettings') {
			// 打开设置面板
			const settingsButton = document.querySelector('#baoguoButton');
			if (settingsButton) {
				settingsButton.click();
			}
		}
	});

	/**
	 * 主程序执行函数。
	 *
	 * 1. 获取用户的设置。
	 * 2. 检查是否需要进行更新。
	 * 3. 执行基础功能函数。
	 */
	function main() {
		// 清理可能存在的旧状态
		if (window.infiniteScrollCleanup) {
			window.infiniteScrollCleanup();
		}

		const settings = getSettings();

		baseFunction(settings);
		// bgsh_initializeImagePreviewFeature(); // This line is removed as the function is not defined
		// Removed call to applyImagePreviewState as it's being refactored
	}

	// 启动主程序
	main();

	/**
	 * 启用拖动
	 * @param {string} elmnt - 组件。
	 */
	function enableDrag(elmnt) {
		var initialX = 0, initialY = 0, currentX = 0, currentY = 0, offsetX = 0, offsetY = 0;
		var latestMouseX = 0, latestMouseY = 0; // Store latest mouse coords
		var rafId = null; // To store the requestAnimationFrame ID
		var isDragging = false; // More explicit dragging state

		var dragHandle = document.getElementById(elmnt.id + "Header") || elmnt;

		dragHandle.onmousedown = dragMouseDown;

		function dragMouseDown(e) {
			e = e || window.event;
			const target = e.target;
			// 优化交互元素检查，增加对链接 (A) 和 customModal 内元素的判断
			const isInteractive =
				target.tagName.match(/^(INPUT|TEXTAREA|BUTTON|SELECT|OPTION|LABEL|A)$/) || // 增加 A 标签
				target.closest('button') !== null || // 检查是否点击在按钮上
				target.closest('a') !== null || // 检查是否点击在链接上
				target.closest('.tattl') !== null || // 防止点击附件项时触发拖拽
				target.closest('#customModal input') !== null || // 明确排除 modal 内的 input
				target.closest('#customModal a') !== null || // 明确排除 modal 内的链接
				target.closest('#customModal button') !== null || // 明确排除 modal 内的按钮
				target.classList.contains("bgsh-checkbox-custom") ||
				target.classList.contains("bgsh-setting-checkbox-custom") ||
				target.classList.contains("bgsh-label-text") ||
				target.closest(".bgsh-setting-checkbox-label") !== null;

			if (isInteractive) {
				return;
			}
			e.preventDefault();

			initialX = e.clientX;
			initialY = e.clientY;
			latestMouseX = initialX; // Initialize latest coords
			latestMouseY = initialY;
			const rect = elmnt.getBoundingClientRect();
			currentX = rect.left;
			currentY = rect.top;
			offsetX = initialX - currentX;
			offsetY = initialY - currentY;

			elmnt.style.transform = 'none';
			elmnt.style.left = currentX + 'px';
			elmnt.style.top = currentY + 'px';

			isDragging = true; // Set dragging state
			// 移除设置十字指针的代码
			// document.body.style.cursor = 'crosshair';
			// elmnt.style.cursor = 'crosshair';
			// 确保元素在拖动开始时就有 move 指针（如果之前没有）
			if (window.getComputedStyle(elmnt).cursor !== 'move') {
				elmnt.style.cursor = 'move';
			}

			document.addEventListener('mouseup', closeDragElement, true);
			document.addEventListener('mousemove', elementDrag, false); // Use false for bubbling phase listener

			// elmnt.dataset.isDragging = "true"; // Using isDragging variable now
		}

		// This function is called by mousemove, it only updates coords and schedules an update
		function elementDrag(e) {
			if (!isDragging) return; // Exit if not dragging

			e = e || window.event;
			// No preventDefault here, mousemove default is usually fine unless selecting text

			// Update latest mouse position
			latestMouseX = e.clientX;
			latestMouseY = e.clientY;

			// Schedule an update if one isn't already scheduled
			scheduleUpdate();
		}

		// Schedules the actual DOM update using rAF
		function scheduleUpdate() {
			if (rafId === null) { // Only schedule if no frame is pending
				rafId = requestAnimationFrame(updateElementPosition);
			}
		}

		// This function is called by requestAnimationFrame to update the DOM
		function updateElementPosition() {
			if (!isDragging) { // Double check if dragging stopped before frame executed
				 rafId = null; // Clear rafId if dragging stopped
				 return;
			}

			// Calculate new position based on the latest stored mouse coordinates
			const newPosX = latestMouseX - offsetX;
			const newPosY = latestMouseY - offsetY;

			// Update the element's position
			elmnt.style.left = newPosX + "px";
			elmnt.style.top = newPosY + "px";

			// Reset rafId so the next mousemove event can schedule a new frame
			rafId = null;
		}


		function closeDragElement() {
			if (isDragging) { // Only run if dragging was in progress
				isDragging = false; // Clear dragging state
				// 移除恢复 body 指针的代码
				// document.body.style.cursor = 'default';
				// 确保元素在拖动结束后保持 move 指针
				elmnt.style.cursor = 'move';

				// Cancel any pending animation frame
				if (rafId !== null) {
					cancelAnimationFrame(rafId);
					rafId = null;
				}

				document.removeEventListener('mouseup', closeDragElement, true);
				document.removeEventListener('mousemove', elementDrag, false); // Make sure to match the phase used in addEventListener

				// Mark that the element has been dragged at least once (for initial positioning logic)
				elmnt.dataset.hasBeenDragged = "true";
			}
		}
	}

	/**
	 * 创建一个多选框组。
	 * @param {string} id - 组件的ID。
	 * @param {string} title - 多选框组的标题。
	 * @param {Array} options - 多选框选项数组，每个选项包含"value"和"label"属性。
	 * @returns {HTMLElement} 返回多选框组的HTML元素。
	 */
	function createCheckboxGroup(id, title, options) {
		const groupDiv = document.createElement("div");
		groupDiv.className = "bgsh-forget";
		groupDiv.id = id;

		let innerHTML = `<strong>${title}</strong><br>`;

		// 添加一个'全选'复选框
		const selectAllId = `bgsh-${id}-select-all`;
		innerHTML += `
      <label class="bgsh-checkbox-label">
          <input type="checkbox" id="${selectAllId}" class="select-all">
          <span class="bgsh-checkbox-custom"></span>
          <span class="bgsh-label-text">全选</span>
      </label>
  `;

		options.forEach((option) => {
			const checkboxId = `bgsh-${id}-${option.value}`;
			innerHTML += `
          <label class="bgsh-checkbox-label">
              <input type="checkbox" id="${checkboxId}" value="${option.value}">
              <span class="bgsh-checkbox-custom"></span>
              <span class="bgsh-label-text">${option.label}</span>
          </label>
  `;
		});

		groupDiv.innerHTML = innerHTML;

		// 添加事件监听
		const selectAllCheckbox = groupDiv.querySelector(".select-all");
		const otherCheckboxes = Array.from(
			groupDiv.querySelectorAll('input[type="checkbox"]')
		).filter((cb) => cb !== selectAllCheckbox);

		function checkIndeterminateStatus() {
			const checkedCheckboxes = otherCheckboxes.filter(
				(cb) => cb.checked
			).length;

			selectAllCheckbox.checked = checkedCheckboxes === otherCheckboxes.length;
			selectAllCheckbox.indeterminate =
				checkedCheckboxes > 0 && checkedCheckboxes < otherCheckboxes.length;
		}

		// 初始化全选框状态
		setTimeout(() => {
			checkIndeterminateStatus();
		}, 500);
		// 为 '全选' 复选框添加事件监听器
		selectAllCheckbox.addEventListener("change", () => {
			otherCheckboxes.forEach((checkbox) => {
				checkbox.checked = selectAllCheckbox.checked;
			});

			// 在全选/取消全选后更新状态
			checkIndeterminateStatus();
		});

		// 为其他复选框添加事件监听器
		otherCheckboxes.forEach((checkbox) => {
			checkbox.addEventListener("change", checkIndeterminateStatus);
		});

		return groupDiv;
	}

	function createSetAnswerToPostButton() {
		// 你可以根据需要实现功能，这里先返回一个占位按钮
		return createButton(
			"setAnswerToPost",
			"设为答案",
			() => {
				showTooltip("设为答案功能暂未实现");
			},
			"bgsh-setAnswerToPostBtn"
		);
	}
})();
