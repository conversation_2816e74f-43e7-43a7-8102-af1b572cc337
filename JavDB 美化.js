// ==UserScript==
// @name         JavDB 网站美化
// @description  为 JavDB 网站添加圆角、阴影效果、标签按钮间距调整，以及详细页面优化。
// <AUTHOR>
// @version      2.20
// @match        http*://javdb.com/*
// @run-at       document-start
// @grant        GM_addStyle
// @grant        GM_setValue
// @grant        GM_getValue
// @license      MIT
// ==/UserScript==
(function () {
    "use strict";

// 初始隐藏页面，等待样式加载完成
    const hideCss = `
body { visibility: hidden !important; opacity: 0 !important; transition: opacity 0.3s ease !important; }
body.script-ready { visibility: visible !important; opacity: 1 !important; }
`;
    const hideStyle = document.createElement('style');
    hideStyle.id = "javdb-hide-style";
    hideStyle.textContent = hideCss;
    document.documentElement.appendChild(hideStyle);

// 定义背景选项
    const backgroundOptions = [
        {
            id: 'bg1',
            name: '背景1',
            styles: {
                backgroundImage: 'url(https://i.postimg.cc/y8000000/1.png)',
                filter: 'blur(100px) saturate(180%)',
                opacity: 0.3,
                transform: 'rotate(16deg)',
                backgroundSize: '150%',
                top: '-50%',
                left: '-90%'
            }
        },
        {
            id: 'bg2',
            name: '背景2',
            styles: {
                backgroundImage: 'url(https://i.postimg.cc/y8000000/2.png)',
                filter: 'blur(100px) saturate(380%)',
                opacity: 0.35,
                transform: 'rotate(273deg)',
                backgroundSize: '30%',
                top: '-80%',
                left: '-47%'
            }
        },
        {
            id: 'bg3',
            name: '背景3',
            styles: {
                backgroundImage: 'url(https://i.postimg.cc/y8000000/3.png)',
                filter: 'blur(110px) saturate(200%)',
                opacity: 0.55,
                transform: 'rotate(10deg)',
                backgroundSize: '30%',
                top: '-50%',
                left: '-90%'
            }
        },
        {
            id: 'bg4',
            name: '背景4',
            styles: {
                backgroundImage: 'url(https://i.postimg.cc/y8000000/3.png)',
                filter: 'blur(100px) saturate(150%)',
                opacity: 0.35,
                transform: 'rotate(10deg)',
                backgroundSize: '40%',
                top: '-45%',
                left: '-95%'
            }
        }
    ];

// 获取当前选中的背景索引（如果不存在，则默认为0）
    const currentBgIndex = GM_getValue('javdb_bg_index', 0);

// 创建和插入基础样式（立即执行，不依赖DOM）
    const baseStyle = document.createElement('style');
    baseStyle.id = "javdb-base-style";
    baseStyle.type = 'text/css';
    baseStyle.innerHTML = `
html, body {
    margin: 0;
    background: none !important;
    background-image: none !important;
    position: relative;
}

.gradient-background {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    overflow: hidden;
    border-radius: 0px !important;
}

.gradient-background::before {
    content: '';
    position: absolute;
    width: 200%;
    height: 200%;
    transition: all 1.2s ease;
    background-repeat: repeat;
    z-index: 1;
    top: ${backgroundOptions[currentBgIndex].styles.top};
    left: ${backgroundOptions[currentBgIndex].styles.left};
    background-size: ${backgroundOptions[currentBgIndex].styles.backgroundSize};
    background-image: ${backgroundOptions[currentBgIndex].styles.backgroundImage};
    filter: ${backgroundOptions[currentBgIndex].styles.filter};
    opacity: ${backgroundOptions[currentBgIndex].styles.opacity};
    transform: ${backgroundOptions[currentBgIndex].styles.transform};
    animation: ${backgroundOptions[currentBgIndex].styles.animation};

}

/* 背景切换器样式 */
.bg-switcher {
    position: fixed;
    bottom: 60px;
    right: 10px;
    display: flex;
    gap: 12px;
    z-index: 9999;
    padding: 8px;
    border-radius: 30px !important;
    background-color: rgba(255, 255, 255, 0.5);
    backdrop-filter: blur(5px);
    box-shadow: 0 1px 12px rgba(0, 0, 0, 0);
    transition: all 0.3s ease;
    opacity: 1;
    flex-direction: column;
}

.bg-switcher:hover {
    opacity: 1;
    background-color: rgba(255, 255, 255, 0.4);
}

.bg-option {
    width: 12px;
    height: 12px;
    border-radius: 50% !important;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.bg-option:hover {
    transform: scale(1.2);
}

.bg-option.active {
    transform: scale(1.3);
    border: 2px solid #ffffff;
    background: #63CA56 !important;
}

.bg-option::after {
    content: attr(title);
    position: absolute;
    top: -5px;
    right: 10%;
    transform: translateX(0%) scale(0);
    background-color: rgba(0, 0, 0, 0.5);
    color: white;
    padding: 2px 8px;
    border-radius: 6px;
    font-size: 10px;
    white-space: nowrap;
    opacity: 0;
    transition: all 0.3s ease;
    pointer-events: none;
}

.bg-option:hover::after {
    opacity: 1;
    transform: translateX(-50%) scale(1);
}
#bg-option-3, #bg-option-2, #bg-option-1, #bg-option-0 {
    background: rgba(0, 0, 0, 0.20);
}
    `;
    document.documentElement.appendChild(baseStyle);

    // 待页面结构开始形成时，添加背景层
    const addBackgroundLayer = () => {
        if (document.body) {
            if (!document.querySelector('.gradient-background')) {
                const gradientDiv = document.createElement('div');
                gradientDiv.className = 'gradient-background';
                document.body.appendChild(gradientDiv);
            }

            // 添加背景切换器
            if (!document.querySelector('.bg-switcher')) {
                const bgSwitcher = document.createElement('div');
                bgSwitcher.className = 'bg-switcher';

                // 创建4个选项按钮
                backgroundOptions.forEach((option, index) => {
                    const bgOption = document.createElement('div');
                    bgOption.className = `bg-option ${index === currentBgIndex ? 'active' : ''}`;
                    bgOption.id = `bg-option-${index}`;
                    bgOption.setAttribute('title', option.name);

                    // 添加点击事件
                    bgOption.addEventListener('click', () => {
                        // 更新活动状态
                        document.querySelectorAll('.bg-option').forEach(opt => opt.classList.remove('active'));
                        bgOption.classList.add('active');

                        // 更新背景样式
                        updateBackground(index);

                        // 保存选择
                        GM_setValue('javdb_bg_index', index);
                    });

                    bgSwitcher.appendChild(bgOption);
                });

                document.body.appendChild(bgSwitcher);
            }

            return true;
        }
        return false;
    };

    // 更新背景样式
    const updateBackground = (index) => {
        // 查找所有样式表
        let gradientRule = null;
        for (let i = 0; i < document.styleSheets.length; i++) {
            try {
                const rules = document.styleSheets[i].cssRules || document.styleSheets[i].rules;
                for (let j = 0; j < rules.length; j++) {
                    if (rules[j].selectorText && rules[j].selectorText.includes('.gradient-background::before')) {
                        gradientRule = rules[j];
                        break;
                    }
                }
                if (gradientRule) break;
            } catch (e) {
                // 跨域样式表会抛出安全错误，忽略
                continue;
            }
        }

        if (gradientRule) {
            gradientRule.style.backgroundImage = backgroundOptions[index].styles.backgroundImage;
            gradientRule.style.filter = backgroundOptions[index].styles.filter;
            gradientRule.style.opacity = backgroundOptions[index].styles.opacity;
            gradientRule.style.animation = backgroundOptions[index].styles.animation;
            gradientRule.style.transform = backgroundOptions[index].styles.transform;
            gradientRule.style.backgroundSize = backgroundOptions[index].styles.backgroundSize;
            gradientRule.style.top = backgroundOptions[index].styles.top;
            gradientRule.style.left = backgroundOptions[index].styles.left;
        } else {
            // 如果找不到规则，直接操作DOM
            const gradientBg = document.querySelector('.gradient-background::before');
            if (gradientBg) {
                gradientBg.style.backgroundImage = backgroundOptions[index].styles.backgroundImage;
                gradientBg.style.filter = backgroundOptions[index].styles.filter;
                gradientBg.style.opacity = backgroundOptions[index].styles.opacity;
                gradientBg.style.animation = backgroundOptions[index].styles.animation;
                gradientBg.style.transform = backgroundOptions[index].styles.transform;
                gradientBg.style.backgroundSize = backgroundOptions[index].styles.backgroundSize;
                gradientBg.style.top = backgroundOptions[index].styles.top;
                gradientBg.style.left = backgroundOptions[index].styles.left;
            }
        }
    };

    // 尝试添加背景层，如果DOM还未准备好，则设置观察器
    if (!addBackgroundLayer()) {
        const bodyObserver = new MutationObserver(() => {
            if (addBackgroundLayer()) {
                bodyObserver.disconnect();
            }
        });
        bodyObserver.observe(document.documentElement, { childList: true, subtree: true });
    }

    // 样式内容
    const customStyles = `
*  {
    --border-line: 1px solid rgba(255, 255, 255, 0.4);
    --background-color: rgba(255, 255, 255, 0.5);
    --outline-rgba: rgba(0, 0, 0, 0.1);
}

strong {
    font-weight: 700 !important;
}

/* 全局背景色 */
body {
    text-decoration: none !important;
}

/* 全局圆角 */
* {
    border-radius: 10px !important;
}

/* 全局卡片白底 */
.item {
    background-color: rgba(255, 255, 255, 0.8) !important;
}

/* 全局按钮 */
.button {
background-color: rgba(255, 255, 255, 0.8);
border-color: #fff;
border-width: 1px;
border-radius: 10px !important;
color: #888;
}

/* 全局按钮（hover） */
.button:hover {
	border-color: rgba(255, 255, 255, 0.8) !important;
}

/* 全局按钮点击效果 */
.button.is-info:active {
    transform: scale(1.5);
}

/*  ---- ---- ---- ----顶部导航栏 ---- ---- ---- ---- */
.navbar {
  position: relative;
  background-color: rgba(244, 58, 75, 0.5) !important;
  border-radius: 0px !important;
  box-shadow: 0 1px 6px rgba(0, 0, 0, 0.2) !important;
}

.navbar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: inherit;
  backdrop-filter: blur(20px);
  z-index: -1;
  border-radius: 0px;
}

.navbar-dropdown.is-right, .navbar-dropdown.is-boxed, .navbar.is-spaced .navbar-dropdown {
	box-shadow: 0 1px 30px rgba(0, 0, 0, 0.2) !important;
	background: rgba(255, 255, 255, 0.7) !important;
	backdrop-filter: blur(20px) saturate(180%) !important;
}

.navbar-dropdown {
    border-top: none;
}

.document.querySelector {
   background-color: #CE5966 !important;
}

/* 鼠标悬停时改变下拉菜单项的背景色 */
.navbar-item.has-dropdown:hover .navbar-dropdown .navbar-item:hover {
	background-color: transparent !important;
	border-radius: 0px !important;
	color: red;
}

/* SVG 修改 */
rect[fill="#2F80ED"] {
    fill: transparent !important;
}

/* 隐藏广告 */
.list-tab,
.navbar-item[href='/rankings/playback'],  /* 热播 */
.movie-list .item .cover .tag-can-play.cnsub, /* 中字可播放 */
.sub-header, /* 广告 */
.navbar.is-dark.is-fluid {
    display: none !important;
}

/* 去除蓝色线 */
.has-left-sep {
    border: none !important;
}

/* 背景 */
.navbar.is-blackcusbar,
.navbar.is-blackcusbar:hover,
.navbar.is-black:hover {
    background-color: #e45e6b !important;
}
.navbar-dropdown a.navbar-item,
.navbar-dropdown .navbar-item {
  padding: 0 !important;
  text-align: center !important;
  display: flex;
  flex-direction: column;
  align-items: center;
}

#navbar-menu-hero > div > div:nth-child(n+1) > div {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 15px 10px 15px 10px;
    gap: 10px;
    position: absolute;
    left: 37%;
    top: 100%;
    transform: translate(-50%, 0%);
}

.navbar-dropdown:hover a.navbar-item:hover,
.navbar-dropdown:hover .navbar-item:hover {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.navbar-item:hover, .navbar-item.has-dropdown:hover .navbar-link {
  background-color: #e45e6b !important;
  border-radius: 8px !important;
}

.navbar-dropdown .navbar-item,
.navbar-dropdown a.navbar-item {
  padding-right: 0rem !important;
  padding-left: 0rem !important;
}

.navbar-dropdown.is-right {
  display: none;  /* 默认隐藏菜单 */
  flex-direction: column;
  align-items: center;
  gap: 10px;
  padding: 10px;
  position: absolute;  /* 让菜单浮动 */
}

.navbar-item.has-dropdown:hover .navbar-dropdown.is-right {
  display: flex;  /* 鼠标悬停时显示菜单 */
}

.navbar.is-black .navbar-end a.navbar-item:focus,
.navbar.is-black .navbar-end .navbar-link:focus,
.navbar.is-black .navbar-start a.navbar-item:focus,
.navbar.is-black .navbar-start .navbar-link:focus,
.navbar.is-black .navbar-brand a.navbar-item:focus {
  background-color: transparent;
}

/* ---- ---- ---- ---- 搜索---- ---- --- ---- -------*/
 /* 影片和小箭头（左边）*/
        /* 下拉框样式 */
        .search-bar-wrap .search-type select {
            height: 40px !important;
            border: none !important;
            color: #7367B6 !important;
        }
        /* 小箭头 */
        .select:not(.is-multiple):not(.is-loading):after {
            border-color: #7367B6 !important;
            right: 1.125em;
            z-index: 4;
        }
        /* 鼠标悬停时改变箭头颜色 */
        .select:not(.is-multiple):not(.is-loading):hover:after {
            border-color: #7367B6 !important;
        }
        /* 搜索框背景 */
        .search-bar-wrap {
            background-color: rgba(156, 100, 219, 0.6) !important;
            padding: 0.8rem;
            box-shadow: 0 1px 12px rgba(0, 0, 0, 0.1) !important;
            margin-top: 30px !important;
            height: 70px;
        }
        /* 搜索框内输入框样式 */
        .search-bar-wrap .search-input .input {
            box-shadow: none !important;
            border: none !important;
            height: 40px;
        }
        /* 控件整体间距 */
        .search-bar-wrap .control {
            display: flex !important;
            align-items: center !important;
            transform: translateY(-4px);
            margin: 4px !important;
        }
/* 检索按钮组 */
.search-bar-wrap .control .button {
	background-color: rgba(156, 100, 219, 0.6) !important;
	margin: 2px !important;
	height: 40px !important;
	border: none;
}
/* 鼠标悬停按钮效果 */
.search-bar-wrap .control .button:hover {
	background-color: #DB5266 !important;
	border: none !important;
}
        /* 弹出气泡 */
        .tooltip::before {
            border-radius: 8px !important;
            top: -5px !important;
            box-shadow: 0 1px 6px rgba(0, 0, 0, 0.2) !important;
        }
        /* 删除泡泡小箭头 */
        [data-tooltip]:not(.is-disabled):after,
        [data-tooltip]:not(.is-loading):after,
        [data-tooltip]:not([disabled]):after {
            content: none !important;
        }
/* ----猜你喜欢，全部，有码，无码 ----  ---- ------- ---- --- ---- -------*/
      /* 按钮样式 */
        .pagination-link.is-current,
        .float-buttons a {
            color: #fff !important;
            background-color: #AC86C5 !important; /* 设置背景颜色为紫色 */
            border: none !important; /* 无边框*/
            box-shadow: 0 1px 6px rgba(0, 0, 0, 0.1) !important;
        }
         /* 激活按钮的样式 */
        .tabs li.is-active a {
           color: #fff !important;
           border: none !important; /* 无边框 */
           box-shadow: 0 1px 6px rgba(0, 0, 0, 0.1) !important;
           background-color: rgba(156, 100, 219, 0.6) !important; /* 设置背景颜色为紫色 */
        }
        /* 激活按钮的鼠标悬停样式（不改变任何效果） */
        .tabs li.is-active a:hover {
          color: #fff !important; /* 保持文字颜色 */
          background-color: #AC86C5 !important; /* 保持背景颜色 */
        }
        /* 鼠标悬停时的样式（其他按钮） */
        .tabs li a:hover {
          color: #AC86C5 !important; /* 鼠标悬停时的文字颜色 */
        }
        /* 按钮*/
        .tabs.main-tabs.is-boxed {
            margin-left: -18px !important; /* 左对齐*/
            margin-bottom: 10px !important;/* 上下间距*/
        }
/* ----删除  -------*/
        /* 移除tab底部边框 */
        .tabs ul {
            border-bottom: none !important;
        }
        /* 移除主页顶部和详细页tab蓝色分隔线 */
        .tabs.is-boxed,
        .tabs.no-bottom {
            border-bottom: 0 !important;
            margin-bottom: 10px !important;
        }
/* ----大封面，小封面 ----  ---- ------- ---- --- ---- -------*/
       /* 按钮组 hover 样式 */
.buttons.has-addons :hover {
	color: #Ed7676 !important;
}
.button.is-active, .button:active {
	border-color: #fff;
	color: #363636;
}
.button.is-focused:not(:active), .button:focus:not(:active) {
	box-shadow: none;
}
/* 激活按钮 */
        .buttons.has-addons .button.is-selected {
            background-color: #Ed7676 !important; /* 默认背景颜色 */
            border-color: #Ed7676; /* 默认边框颜色 */
            border-width: 1px;
            color: #fff; /* 默认字体颜色 */
        }
        /* 鼠标悬停时的样式 */
        .buttons.has-addons .button.is-selected:hover {
            color: #fff !important; /* 鼠标悬停时的字体颜色 */
            border-color: #Ed7676 !important; /* 鼠标悬停时的边框颜色 */
            background-color: #Ed7676; /* 保持背景颜色不变（可选） */
        }
      /* 按钮组样式调整 */
        .buttons.has-addons > .button {
            margin: 0 !important; /* 去除按钮之间的间距 */
            border-radius: 0 !important; /* 移除单独按钮的圆角 */
         }
        /* 调整按钮组首尾按钮圆角 */
        .buttons.has-addons > .button:first-child {
            border-top-left-radius: 8px !important;
            border-bottom-left-radius: 8px !important;
        }
        .buttons.has-addons > .button:last-child {
            border-top-right-radius: 8px !important;
            border-bottom-right-radius: 8px !important;
            border-left-width: 1px !important; /* 确保最后一个按钮和前一个按钮显示正常边框 */
        }
        .buttons.has-addons > .button:not(:first-child) {
            border-left: none !important; /* 移除中间按钮的左边框 */
            /* border-right: none !important; /* 移除中间按钮的左边框 */
        }
/*  ---- ---- ---- ---- ---- 主页更新页面 ---- ---- ---- ---- ----*/
 /* 1.卡片 */
        /*整体布局*/
        .item {
            border: 0px solid #fff !important; /* 设置边框 */
            margin-left: 0 !important; /* 左对齐 */
            margin-right: 0 !important; /* 右对齐 */
            transform: translateY(10px); /* 向下移动 */
        }

.box {
	background-color: transparent;
	border: none !important;
}
        /*卡片间距*/
        .movie-list {
            display: grid;
            grid-column-gap: .4rem;
            -moz-column-gap: .4rem;
            column-gap: 1.2rem;
            grid-row-gap: 1rem;
            row-gap: 1.6rem;
            grid-template-columns: repeat(4,minmax(0,1fr));
            padding-bottom: .5rem;
        }
        /* 鼠标指向卡片边框 */
        a.box:focus, a.box:hover {
           outline: none;
           box-shadow: none;
           }
         .item:focus, .item:hover {
           outline: 4px solid #Ed7676; /* 修改为橙色边框 */
           box-shadow: 0 0 12px 5px rgba(238, 149, 149, 0.5);
         }
/* 2.封面 */
          /* 封面填充 */
          .movie-list .item .cover.contain img {
              width: 100%;
              object-fit: cover;
          }
         /* 封面圆角 */
        .item .cover img {
            border-bottom-left-radius: 0 !important;  /* 默认移除底部圆角 */
            border-bottom-right-radius: 0 !important; /* 默认移除底部圆角 */
        }
        /* 封面图背景 */
          .movie-list .item .cover {
              background: #fff;
              border-bottom-left-radius: 0 !important;  /* 默认移除底部圆角 */
              border-bottom-right-radius: 0 !important; /* 默认移除底部圆角 */
          }
        /* 鼠标悬停状态*/
        .item .cover img:hover {
            border-radius: 8px !important; /* 保证放大时四个角有圆角 */
            border-bottom-left-radius: 8px !important;  /* 恢复底部左角圆角 */
            border-bottom-right-radius: 8px !important; /* 恢复底部右角圆角 */
        }
        .item .video-title {
            border-radius: 0 !important;  /* 免疫标题部分的圆角 */
        }
/* 3.标题 */
        /* 修改未访问链接的颜色 */
        .movie-list .box .video-title {
            color: #444444 !important;  /* 修改未访问链接的标题颜色为灰色 */
        }
        /* 两行显示*/
        .movie-list .item .video-title {
            font-size: 14px !important;               /* 设置字体大小 */
            padding-top: 0.2rem;                      /* 控制顶部间距 */
            padding-bottom: 0.2rem;                   /* 控制底部间距 */
            white-space: normal;                      /* 允许换行 */
            overflow: hidden;                         /* 隐藏超出容器的内容 */
            text-overflow: ellipsis;                  /* 超出部分显示省略号 */
            line-height: 1.5rem;                      /* 每行文字高度 */
            height: 3.5rem;                           /* 设置容器高度为两行文字略多 */
            display: -webkit-box;                     /* Flex 布局，支持多行截断 */
            -webkit-box-orient: vertical;             /* 垂直布局 */
            -webkit-line-clamp: 2;                    /* 限制最多显示两行 */
         }
/* 4.评分 */
         .movie-list .item .score {
            padding-top: 0.4rem;                 /* 设置顶部边距 */
            padding-bottom: .2rem;              /* 设置底部边距 */
            color: #333;
            font-size: 12px;
          }
/* 5.日期 */
          .movie-list .item .meta {
              padding-bottom: .2rem;
              color: #333;
              font-size: 12px;
          }
/* 6.标签 */
       /* 标签间距 */
        .tags.has-addons .tag, .tags.is-right .tag:not(:last-child) {
            margin-right: 10px; /* 恢复默认值 */
        }
.tag:not(body).is-info {
	background-color: #3e8ed0  !important;
	color: #fff;
}

#tags dt.collapse {
	height: 45px;
}
.button.is-info.is-outlined {
color: #fff !important;
	border-color: transparent  !important;
}
/* 含中字磁链 */
.tag.is-warning {
    color: #fff !important; /* 设置字体颜色 */
    background-color: #f4b16b !important; /* 背景色为黑色 */
}
.button.is-info.is-focused:not(:active), .button.is-info:focus:not(:active) {
	box-shadow: none;
}
/* 昨日新种 */
.tag:not(body) {
    background-color: rgba(0, 0, 0, 0.20) !important;
    border-radius: 8px !important;
    color: #fff;
}
.tag:not(body).is-success {
	background-color: #48c78e !important;
	color: #fff;
}
.tags .tag {
    margin-bottom: .3rem;
    border-radius: 8px !important;
}

/*  ---- 底部翻页按钮----*/
/* 去横线 */
nav.pagination {
    margin-top: 1.5rem;
    padding-top: 1.5rem;
    border-top: 0px solid hsla(0,0%,85.9%,.5);
}

/* 底部当前页激活时的翻页按钮颜色 */
.pagination-link.is-current:active {
  background-color: transparent;
  border: none !important;

}

/* 底部按钮（还包括部分顶部按钮） */
.pagination-ellipsis, .pagination-link, .pagination-next, .pagination-previous, .select select, .textarea {
    color: #856599 !important;
    border: none;
    font-size: .85em;
}

.pagination-link, .pagination-next, .pagination-previous {
      background-color: transparent;
}

/* 去掉省略号阴影 */
.pagination-ellipsis {
    box-shadow: none !important;
}

/* 底部按浮动按钮 */
.float-buttons {
    padding: 0.2rem!important;
    display: flex!important;
    gap: 10px!important;
}

.movie-list .item .cover.contain img, .movie-list .item .cover img {
    transition: transform .5s ease;
}

.pagination-link:active, .pagination-next:active, .pagination-previous:active {
    box-shadow: none;
    color: #E45E6B !important;
}

.app-desktop-banner .container {
    display: none !important;
}

/* ---- ---- ---- ---- ---- --- ---- ---- ---2.详细页 ---- ---- --- ---- ---- --- ---- ---- ---- -------*/
/*  ---- ----2.0 封面 ---- ---- -*/
.column-video-cover {
	padding: 1.5rem;
}
        .column-video-cover img {
            outline: 6px solid var(--outline-rgba) !important;
            margin-top: 5px;
        }
/*  ---- ---- ---- ---- 2.1 封面右边详细信息---- ---- ---- ---- -*/
/* 卡片背景色 */
        .message-body {
            background-color: transition !important;
        }
/* 行高 */
        .movie-panel-info .panel-block {
            line-height: 1.5;     /* 设置行内间距 */
            margin-bottom: 10px; /* 设置行间距 */
        }
        .movie-panel-info .panel-block:last-child {
            margin-bottom: 0; /* 移除最后一行的额外间距 */
        }
/* 隐藏指定的元素 */
        /* 去分隔线 */
        .panel-block {
            border-bottom: none !important; /* 番号，日期，时长，片商 */
        }
        /* 其他 */
        .buttons.are-small.review-buttons,  /* 下载，修正 */
        .play-button,
        .list-tab,
        .is-size-7.has-text-grey, /* 多少人想看 */
        .sub-header, /* 广告 */
        .top-meta, /* 官方app，telegram频道 */
        .search-recent-keywords,
        .navbar.is-dark.is-fluid {
            display: none !important;
        }
/* 右边文字 */
        /* 复制按钮 */
       .button.is-white.copy-to-clipboard {
           box-shadow: none !important; /* 去阴影 */
         }
       .button.is-white.copy-to-clipboard:hover{
           background-color: transparent !important; /* hover 背景色 */
         }
         .button.is-white {
	background-color: transparent;
}
/* 其他 */
       .panel.movie-panel-info .panel-block {
           font-size: 14px !important; /* 示例字体大小 */
         }
        .panel.movie-panel-info .panel-block a {
            color: #333 !important; /* 超链接的颜色 */
        }
        .panel.movie-panel-info a:hover {
            color: #ef9595 !important; /* 修改鼠标悬停时的超链接颜色为浅番茄红 */
        }
        .score-stars [class^=icon-] {
            color: #ED7676 !important; /* 星星得分 */
        }
.score-stars .gray {
	color: rgba(0, 0, 0, 0.20) !important;
}
.video-panel .magnet-links .item .magnet-name .name {
	font-weight: 600 !important;
	font-size: 14px !important;
}
/*  ---- ---- ---- ---- 2.2 预览图框格---- ---- ---- ---- ----*/
  /*整体布局*/
.preview-images {
	display: grid !important;
	grid-template-columns: repeat(auto-fill, minmax(120px, 1fr)) !important;
	grid-gap: 20px !important;
}
        /* 确保每个框格的图片按原始比例展示，且黑色填充空白 */
.preview-images img {
	width: 100% !important;
	height: 100% !important;
	object-fit: cover !important;
	background-color: var(--background-color) !important;
	display: block !important;
	aspect-ratio: 16 / 10 !important;
	outline: 4px solid var(--outline-rgba) !important;
}
        /* 确保每个框格的容器用黑色背景填满整个区域 */
        .preview-images .item {
            background-color: black !important; /* 背景色为黑色 */
            display: flex !important; /* 使用flex布局来让图片居中 */
            justify-content: center !important; /* 水平居中 */
            align-items: center !important; /* 垂直居中 */
            height: 100% !important; /* 确保框格的高度填满容器 */
            width: 100% !important; /* 让图片宽度填满框格 */
        }
 /* 视频预览 */
        /* 修改 preview-video-container 字体大小和颜色 */
        .preview-video-container span {
            font-size: 11px !important; /* 设置字体大小 */
            border-radius: 3px !important;
            color: #FFFFFF !important; /* 设置字体颜色 */
            position: absolute !important; /* 按钮绝对定位 */
            top: 10% !important; /* 相对于父容器的垂直居中 */
            left: 5% !important; /* 距离左侧 */
        }
         /* 去掉视频预览遮罩 */
        .preview-video-container:after {
            background-color: rgba(0,0,0,.0);
        }
      .tag.is-dark {
                  background-color:#000 !important; /* 背景色为黑色 */
              }
      .tag.is-warning.tooltip {
                  background-color:#ff90bc !important; /* 背景色为黑色 */
              }
/*  ---- ---- ---- ---- ---- 2.3 下载页 ---- ---- ---- ---- ----*/
        /* 移除边框残影 */
        .tabs a {
            border-bottom: none !important;
            margin-bottom: 0px;
        }
       /* 列居中 */
        .item.columns.is-desktop,
        .item.columns.is-desktop.odd {
            margin: 6px !important;
            transform: translateY(-4px); /* 向下移动按钮组 10 像素 */
        }
        .item.columns.is-desktop:hover,
        .item.columns.is-desktop.odd:hover {
            background-color: #fff !important;
        }
        /* 按钮组 */
        .tabs.is-boxed, .tabs.no-bottom {
            border-bottom: 0 !important;
            margin-bottom: 0px !important;
            overflow: visible;
        }
        /* 背景透明 */
        .tabs.is-boxed a {
            background-color: transparent !important;
        }
        /* name、meta 和 tags 列整体右移 20px */
        .magnet-links .item .magnet-name,
        .magnet-links .item .magnet-meta,
        .magnet-links .item .magnet-tags {
            margin-left: 10px !important; /* 向右移动 20px */
        }
         /* 高清 */
        .tag.is-primary.is-small.is-light {
            color: #fff !important; /* 设置字体颜色 */
            background-color: #49C78E !important; /* 背景色为黑色 */
        }
          /* 字幕 */
        .tag.is-warning.is-small.is-light {
            color: #fff !important; /* 设置字体颜色 */
            background-color: #f4b16b !important; /* 背景色 */
        }
          /* meta(大小，文件个数）) */
        .video-panel .magnet-links .item .magnet-name .meta {
            color: #888888 !important; /* 设置字体颜色 */
            font-size: 11px !important;
        }
        /* 磁力链接按钮位置，颜色 */
        .magnet-links .item .buttons {
            position: absolute !important; /* 按钮绝对定位 */
            top: 50% !important; /* 相对于父容器的垂直居中 */
            right: 120px !important; /* 距离右侧调整至 120px，可根据需要手动调整 */
            transform: translateY(-50%) !important; /* 确保垂直方向真正居中 */
            display: flex !important; /* 使用 flex 布局方便调整间距 */
            gap: 0px !important; /* 复制和下载按钮之间的间距，调整为 10px */
            color: #888888 !important; /* 设置字体颜色 */
        }
 /*----复制，下载 ----*/
          /*按钮（复制，下载，赞，收藏）  */
          .button.is-info {
              background-color: #Ed7676 !important;
              color: #fff;
              transition: transform 0.8s ease;
          }
          /*按钮居中 */
          .buttons.column {
              display: flex;
              flex-wrap: wrap; /* 确保按钮不重叠，并且能换行 */
              justify-content: flex-start; /* 按钮的排列方式 */
              align-items: center; /* 按钮垂直居中 */
          }
          .buttons.column > .button.is-info.is-small {
              color: #fff;
              margin: 6px;
          }
        /*按钮（复制，下载，赞，收藏）  */
        .button.is-info.is-small:hover {
            border-color: transparent;
            color: #fff;
        }
        /* 日期 */
        .magnet-links .item .date {
            position: absolute !important; /* 日期列绝对定位 */
            top: 50% !important; /* 垂直居中 */
            right: 15px !important; /* 距离右侧调整至 10px */
            transform: translateY(-50%) !important; /* 确保垂直方向真正居中 */
        }
        /* 短评 */
        .review-items .review-item {
            padding: .8rem 0;
            border-bottom: none;
            border-radius: 0px !important; /* 保证图片顶部圆角 */
            font-size: 14px !important;
        }
        /* 隐藏更多短评 */
        .review-item.more.has-text-link {
              display: none !important;
        }
/*  -------- ---- ---- ----  2.5 其他---- ---- ---- ----  ----*/
/* header颜色 */
        .video-panel .message-header {
            background-color: transparent !important;
            color: #4a4a4a;
            font-size: 15px;
            border-radius: 6px !important; /* 保证放大时四个角有圆角 */
            border-bottom-left-radius: 0px !important;
            border-bottom-right-radius: 0px !important;
        }
/* 链接颜色 */
        .video-panel .tile-images .tile-item .video-number, .video-panel .tile-images .tile-item .video-title {
            color: #888 !important;
            font-size: .7rem;
        }
/* 卡片化 */
  .tile-images.tile-small .tile-item  {
    border: 0px solid #ccc; /* 添加浅灰色边框 */
    box-shadow: 0 1px 6px rgba(0, 0, 0, 0.1) !important; /* 阴影*/
    padding: 10px;         /* 内部内容的间距 */
    background-color: #fff; /* 可选，背景颜色 */
  }
/* ---- ---- ---- ---- ---- --- ---- ---- ---3.女优主页---- ---- --- ---- ---- --- ---- ---- ---- -------*/
/* 头像 */
     .avatar {
            object-fit: cover !important; /* 强制图片填满框格 */
            background-color: #Ed7676 !important; /* 背景色为黑色 */
            display: block !important; /* 图片作为块级元素显示 */
            aspect-ratio: 16 / 16 !important; /* 设置 16:9 的固定长宽比 */
            box-shadow: 0 1px 10px rgba(0, 0, 0, 0.2) !important;  /* 阴影效果 */
            outline: 5px solid var(--outline-rgba) !important;
            border-radius: 12px !important; /* 保证图片顶部圆角 */
        }
        .has-text-justified {
            font-size: 1.5rem;
            text-align: justify!important;
            margin-left: 16px;
}
/* 收藏,twitter,instagram. */
          /*按钮左对齐，增加间距*/
          .column.section-addition > .field.has-addons {
              display: flex;        /* 启用 Flexbox 布局 */
              align-items: center;  /* 垂直方向居中对齐 */
              justify-content: flex-start; /* 保证按钮组左对齐 */
          }
          .column.section-addition > .field.has-addons > .control:not(:first-child) {
              margin-left: 10px; /* 从第二个按钮开始设置水平间距 */
          }
        /* 心形图标 */
        .icon-heart-o {
            display: flex;
            justify-content: flex-end;
            align-items: center;
            width: 20px;
            height: 20px;
        }
        /* twitter */
        a.button.is-info[href*="twitter.com"] {
            background-color: #028FD6 !important;
        }
        /* instagramr */
        a.button.is-info[href*="instagram.com"] {
            background-color: #b547a3 !important;
        }
/* 标签组 */
        .actor-tags.tags {
            border: none; !important; /* 移除tag底部边框 */
            transform: translateY(20px); /* 向下移动 */
        }
 /* 下移 */
        .actor-tags .collapse {
            height: 30px !important;
        }
 /* 标签文字大小…（全局）  */
        .tag:not(body).is-medium {
            font-size: 0.8rem;
            box-shadow: none !important;  /* 阴影效果 */
        }
 /* "全部"*/
        .tag:not(body).is-link {
            background-color: #AC86C5 !important;
            color: #fff !important;
        }
         div.actor-tags.tags > div > a.button.is-link.tag-expand > span.text:hover {
            color: #fff !important;
}
 /* "可播放，单体作品……"  */
.actor-tags .tag {
	color: #fff !important;
	background-color: rgba(0, 0, 0, 0.20) !important;
}

.actor-tags .tag:hover, .actor-tags .button:hover {
	text-decoration: none !important;
	background-color: rgba(0, 0, 0, 0.8) !important;
}
.tag.is-medium.is-link:hover,
.button.is-link.tag-expand.is-outlined:hover,
.button.is-link.tag-expand:hover {
 color: #fff !important;
}
.tag:not(body) .delete {
   border-radius: 50% !important;
}
.tags .tag:not(:last-child) {
   margin-right: .4rem;
}
/* 更多 */
        /* 按钮大小 */
        .actor-tags .content .tag-expand {
            padding: calc(.9em - 6px) 0.5em;
            font-size: .75rem;
            float: right;
        }
        /* 按钮颜色 */
        .button.is-link.is-outlined,
        .button.is-link {
           background-color:#AC86C5 !important;
           border: none; !important;
           color: #fff !important;
        }
        .button.is-link.is-outlined.tag-expand:hover {
            background-color: #AC86C5 !important; /* 设置背景色为紫色 */
            color: white !important; /* 设置文字颜色为白色 */
        }
/* 隐藏male元素 */
.hidden {
    display: none !important;
}
}
/* ---- ---- ---- ---- ---- --- ---- ---- ---4.女优主页---- ---- --- ---- ---- --- ---- ---- ---- -------*/
#tags dt a.tag-expand {
    float: right;
    margin-top: .6rem;
    font-size: .6rem;
    padding: 2px 5px;
    border-color: #e45e6b !important;
    color: #fff;
}
#tags dt {
	border-bottom: none;
}
.actors .box img {
    min-width: 3rem;
    height: auto;
    transition: all .2s ease-in-out;
    box-shadow: none !important;
    border: none !important;
    border-radius: 0 !important;
}
.actors .box {
	overflow: hidden !important;
	box-shadow: 0 1px 6px rgba(0, 0, 0, 0.1);
	background: rgba(255, 255, 255, 0.8);
}
.actor-box a:visited strong {
    color: #ac86c5 !important;
}
.actor-box a strong {
	padding-top: .6rem;
	padding-bottom: .4rem;
	display: block;
	line-height: 1rem;
	overflow-x: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	color: #DB7094;
}
body > section > div > h3:nth-child(6) {
    font-size: 1.5rem;
    margin-top: 16px;
}
.video-meta-panel {
	background: var(--background-color);
  border: var(--border-line) !important;
}
.panel {
	background: none;
}
.message {
	background-color: var(--background-color);
  border: var(--border-line) !important;
  box-shadow: 0 1px 20px rgba(0, 0, 0, 0.03) !important;
  overflow: hidden;

}
.item.columns.is-desktop, .item.columns.is-desktop.odd {
	background: var(--background-color) !important;
  border: var(--border-line) !important;
  box-shadow: none !important;
  margin: 10px 6px !important;
  transition: all 0.2s ease !important;
}
.item.columns.is-desktop:hover, .item.columns.is-desktop.odd:hover {
	background-color: rgba(255, 255, 255, 0.8) !important;
  border: var(--border-line) !important;
  box-shadow: 0 1px 10px rgba(0, 0, 0, 0.05) !important;
	outline: none;
	scale: 1.02;
}
.tile-images.tile-small .tile-item {
	border: var(--border-line);
	box-shadow: 0 1px 15px rgba(0, 0, 0, 0.04) !important;
	padding: 4px;
	background-color: var(--background-color);
	margin: 0 5px;
}
#reviews,
#magnets {
	margin-top: 4px;
}
.item {
	background-color: rgba(255, 255, 255, 0.8) !important;
  box-shadow: 0 1px 15px rgba(0, 0, 0, 0.05) !important;
}
.item:focus, .item:hover {
	outline: 4px solid #Ed7676;
	box-shadow: 0 0 12px 5px rgba(238, 149, 149, 0.5);
}
.movie-list .item .cover:hover img {
	border-radius: 12px !important;
	box-shadow: 0 1px 30px rgba(0, 0, 0, 0.20) !important;
	outline: 3px solid rgba(245, 245, 245) !important;
}
.movie-panel-info div.panel-block span.value {
	color: #333;
}
.section-container .box {
	background: rgba(255, 255, 255, 0.5) !important;
	border: var(--border-line) !important;
  color: #333;
}
    `;

    // 公共的自动展开功能
    function autoExpandContent() {
        document.querySelectorAll(".replace_tip").forEach(function (replaceTip) {
            const iconExpand = replaceTip.querySelector(".icon-expand");
            if (iconExpand) replaceTip.click();
        });
    }

    // 函数：替换 span.meta 中的逗号为空格
    function replaceCommasInMeta() {
        const metaSpans = document.querySelectorAll("span.meta");
        metaSpans.forEach((span) => {
            if (span.textContent && span.textContent.includes(",")) {
                span.textContent = span.textContent.replace(/,/g, " ");
            }
        });
    }

    // 函数：隐藏包含 'male' 符号的元素及其前一个链接
    function hideMaleElements() {
        document
            .querySelectorAll("strong.symbol.male:not(.hidden)")
            .forEach((element) => {
                const previousLink = element.previousElementSibling;
                if (previousLink && previousLink.tagName === "A") {
                    previousLink.classList.add("hidden");
                }
                element.classList.add("hidden");
            });
    }

    // 应用样式和功能的统一函数
    function applyStyles() {
        // 检查是否已存在样式元素，避免重复注入
        if (!document.getElementById("javdb-main-style")) {
            // 使用GM_addStyle统一注入主样式
            try {
                GM_addStyle(customStyles);
                console.log("JavDB美化: GM_addStyle样式已应用");
            } catch (error) {
                // 如果GM_addStyle失败，则使用备用方式
                console.warn("JavDB美化: GM_addStyle失败，使用备用注入方式", error);
                const styleEl = document.createElement("style");
                styleEl.id = "javdb-main-style";
                styleEl.textContent = customStyles;
                document.head.appendChild(styleEl);
            }
        }

        // 应用DOM修改
        autoExpandContent();
        replaceCommasInMeta();
        hideMaleElements();
    }

    // 根据DOM就绪状态决定何时应用样式
    function init() {
        if (document.readyState === "loading") {
            document.addEventListener("DOMContentLoaded", () => {
                applyStyles();
                // 恢复页面可见性
                document.body.classList.add('script-ready');
            });
        } else {
            applyStyles();
            // 恢复页面可见性
            document.body.classList.add('script-ready');
        }
    }

    // 初始化脚本
    init();

    // 监控DOM变化，确保样式和功能在动态内容上也能应用
    const observer = new MutationObserver((mutationsList) => {
        let needsUpdate = false;
        for (const mutation of mutationsList) {
            if (mutation.type === "childList" && mutation.addedNodes.length > 0) {
                needsUpdate = true;
                break;
            }
        }

        if (needsUpdate) {
            replaceCommasInMeta();
            hideMaleElements();
        }
    });

    // 开始观察整个文档
    observer.observe(document.documentElement, { childList: true, subtree: true });
})();
