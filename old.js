// ==UserScript==
// @name         linux.do 回复重构 + 美化合并版
// @namespace    http://tampermonkey.net/
// @version      1.0
// @description  合并版：回复重构功能 + 美化布局功能，优化性能，移除冗余代码
// <AUTHOR>
// @match        https://linux.do/*
// @grant        GM_addStyle
// @run-at       document-start
// ==/UserScript==

(function() {
    'use strict';

    // ========== 浏览器检测 ==========
    const isFirefox = navigator.userAgent.toLowerCase().includes('firefox');

    // ========== 配置 - 合并优化 ==========
    const CONFIG = {
        DEBUG: false, // 关闭调试模式
        // 统一的延迟配置
        PROCESS_DELAY: isFirefox ? 32 : 16,
        MAX_POSTS_PER_BATCH: isFirefox ? 10 : 20,
        BATCH_SIZE: isFirefox ? 2 : 3,
        THROTTLE_DELAY: isFirefox ? 200 : 100,
        FETCH_DELAY: isFirefox ? 32 : 16,
        MAX_FETCH_ATTEMPTS: 3,
        QUOTE_STYLE_ID: 'optimized-quote-styles',
        BEAUTIFY_STYLE_ID: 'linux-do-beautify-styles',
        CACHE_TTL: 10 * 60 * 1000, // 10分钟缓存
        CACHE_CLEAN_INTERVAL: isFirefox ? 60000 : 120000 // 缓存清理间隔
    };

    // ========== 日志系统 ==========
    const log = CONFIG.DEBUG ? (...args) => console.log(`[${isFirefox ? 'Firefox' : 'Chrome'}]`, ...args) : () => {};

    // ========== 全局状态 ==========
    const postCache = new Map();
    let isProcessing = false;
    let lastProcessTime = 0;
    let rafId = null;

    // ========== 优化的样式系统 - 合并重复样式 ==========
    function initStyles() {
        if (document.getElementById(CONFIG.QUOTE_STYLE_ID)) return;

        const style = document.createElement('style');
        style.id = CONFIG.QUOTE_STYLE_ID;

        // 合并重复的楼层号样式
        const floorNumberStyles = `
            content: attr(data-floor-number) !important;
            position: absolute !important;
            left: 96% !important;
            background: #000 !important;
            color: #fff !important;
            border-radius: 50% !important;
            text-align: center !important;
            display: block !important;
            visibility: visible !important;
            opacity: 0.8 !important;
            pointer-events: none !important;
            top: -5px !important;
            transform: translate(50%, 50%) !important;
            width: 22px !important;
            height: 22px !important;
            font-size: 11px !important;
            line-height: 22px !important;
            box-shadow: 0 1px 6px rgba(0,0,0,0.1) !important;
        `;

        style.textContent = `
            /* 楼层号样式基础 */
            article[id^="post_"] {
                position: relative !important;
            }

            article[id^="post_"] .topic-meta-data::after {
                display: none !important;
            }

            /* 统一的楼层号显示样式 */
            article[id^="post_"][data-floor-number]::after {
                ${floorNumberStyles}
            }

            /* 引用样式 */
            .optimized-reply-quote {
                margin-bottom: 10px !important;
                padding: 8px 12px !important;
                background: rgba(0, 0, 0, 0.05) !important;
                border-radius: 10px !important;
                font-size: 14px !important;
                display: block !important;
                opacity: 1 !important;
                position: relative !important;
                z-index: 300 !important;
            }

            .optimized-reply-quote .quote-header {
                font-weight: bold !important;
                margin: 5px !important;
                color: #555 !important;
                display: flex !important;
                align-items: center !important;
            }

            .optimized-reply-quote .quote-avatar {
                width: 24px !important;
                height: 24px !important;
                border-radius: 4px !important;
                margin-right: 8px !important;
                border: 2px solid rgba(0, 0, 0) !important;
                flex-shrink: 0 !important;
            }

            .optimized-reply-quote .quote-author {
                font-weight: bold !important;
                color: #555 !important;
            }

            .optimized-reply-quote .quote-content {
                color: #666 !important;
                line-height: 1.4 !important;
                word-wrap: break-word !important;
                padding: 0 0 0 4px !important;
            }

            /* 隐藏原始元素 */
            .embedded-posts.top,
            article[id^="post_"]:not([data-quote-processed="true"]) .embedded-posts.top {
                display: none !important;
            }

            .reply-to-tab {
                opacity: 0 !important;
                pointer-events: none !important;
                position: absolute !important;
                z-index: -1 !important;
            }
        `;

        (document.head || document.documentElement).appendChild(style);
    }

    // ========== 美化样式系统 ==========
    function initBeautifyStyles() {
        // 使用GM_addStyle添加美化样式
        GM_addStyle(`
            /* 禁用头像滚动跟随功能 */
            .topic-post.sticky-avatar .topic-avatar {
                position: relative !important;
                top: unset !important;
            }

            body::before, body::after {
                content: "";
                display: none !important;
            }


.topic-map.--bottom,
.sidebar-footer-wrapper,
.d-header-icons,
.nav-pills > li a.active::after, .nav-pills > li button.active::after,
.alert.alert-info,
.names .new_user a, .names .user-title, .names .user-title a,
nav.post-controls,
.small-action.topic-post-visited .topic-post-visited-line,
.timeline-container,
#list-area .show-more,
.house-creative,
.topic-list tr.selected td:first-of-type, .topic-list-item.selected td:first-of-type, .latest-topic-list-item.selected, .search-results .fps-result.selected {
	box-shadow: none !important;
  display: none !important;
}

a {
  color: rgba(0, 0, 0, 0.8) !important;
	text-decoration: none !important;
}

a:hover {
	color: #bb4e5b !important;
}

#post_1 .topic-body, #post_1 .topic-avatar,
img.avatar, img.prefix-image {
	border: none !important;
}

img.avatar {
	border-radius: 10px !important;
}

.list-controls .combo-box .combo-box-header {
	border-radius: 12px !important;
  color: #333 !important;
	border: 1px solid rgba(255, 255, 255, 0.5) !important;
	background: rgba(255, 255, 255, 0.5) !important;
}

.select-kit.combo-box.tag-drop .tag-drop-header, .select-kit.combo-box.tag-drop .selected-name {
  color: #333 !important;
}

.select-kit .select-kit-header {
	border: none !important;
	font-size: 14px!important;
	font-weight: bold !important;
	border-radius: 10px !important;
}

.search-menu .search-input, .search-menu-container .search-input {
	border-radius: 12px !important;
	border: 1px solid rgba(255, 255, 255, 0.5) !important;
	box-shadow: 0 1px 10px rgba(0, 0, 0, 0.05) !important;
	background: rgba(255, 255, 255, 0.5) !important;
}

.header-sidebar-toggle button:focus:hover, .discourse-no-touch .header-sidebar-toggle button:hover {
	background-color: transparent !important;
}

.menu-panel {
	border-radius: 12px !important;
	border: 1px solid rgba(255, 255, 255, 0.5) !important;
	box-shadow: 0 1px 50px rgba(0, 0, 0, 0.1) !important;
	background: rgba(255, 255, 255, 0.7) !important;
  backdrop-filter:blur(20px) saturate(180%) !important;
  margin: 1px 0 !important;
}

.welcome-banner__wrap .results {
	background: transparent !important;
}

.search-random-quick-tip .tip-label {
	background-color: rgba(0, 0, 0, 0.1) !important;backdrop-filter:blur(20px) saturate(180%) !important;
	border-radius: 6px !important;
}

.search-menu .search-link .topic-title, .search-menu-container .search-link .topic-title {
	font-size: 16px !important;
	color: #000000 !important;
	line-height: 2.5;
}

search-menu .search-link:focus, .search-menu .search-link:hover, .search-menu-container .search-link:focus, .search-menu-container .search-link:hover {
	background-color: #E3D6CF70 !important;
}

.search-menu .search-link, .search-menu-container .search-link {
	margin: 20px  !important;
	border-radius: 10px !important;
}


.sidebar-wrapper {
	margin-left: 55px !important;
	border-radius: 12px !important;
	background: rgba(255, 255, 255, 0.7) !important;
}
.sidebar-wrapper .sidebar-container {
	border-right: none !important;
}

.sidebar-section-link-wrapper .sidebar-section-link:focus, .sidebar-section-link-wrapper .sidebar-section-link:hover {
	background: rgba(0, 0, 0, 0.05) !important;
	border-radius: 8px !important;
}
.sidebar-section-wrapper {
	border-bottom: none !important;
}

.sidebar-section-wrapper .sidebar-section-header-wrapper {
	margin-top: 20px !important;
}

.sidebar-section-link-wrapper .sidebar-section-link {
	font-size: 15px;
}

/* 内页帖子卡片式设计 (保留) */
.topic-post, .crawler-post {
  background-color: rgba(255, 255, 255, 0.8) !important;
  border-radius: 12px !important;
  margin-bottom: 15px !important;
  border: 1px solid rgba(255, 255, 255, 0.9) !important;
  overflow: hidden !important;
  padding: 10px !important;
  border: 1px solid rgba(255, 255, 255, 0.3) !important;
  position: relative !important;
}

.topic-post article.boxed, .crawler-post article.boxed {
  border: none !important;
  box-shadow: none !important;
}

.topic-avatar, .crawler-post-meta .creator {
  border-top: none !important;
  padding: 15px 10px 15px 15px !important;
}

.topic-body, .crawler-post .post {
  padding: 15px 15px 10px 0 !important;
}

.topic-meta-data, .crawler-post-meta {
  padding-bottom: 8px !important;
  margin-bottom: 10px !important;
}
.topic-meta-data {
    display: flex !important;
    align-items: flex-start !important;
    flex-direction: column !important;
}

.d-header #site-logo {
	height: var(--d-logo-height)!important;
	width: auto !important;
	max-width: 100% !important;
}

.header-sidebar-toggle button .d-icon {
	width: 100%;
	display: inline-block !important;
	color: rgba(0, 0, 0, 0.8) !important;
}

/* 爬虫模式特殊处理 (保留) */
.crawler-post {
  display: flex !important;
  flex-direction: column !important;
}

.crawler-post-meta {
  display: flex !important;
  align-items: center !important;
  padding: 10px 15px !important;
  background-color: rgba(245, 245, 245, 0.5) !important;
}

.crawler-post .post {
  padding: 15px !important;
}

/* 确保图片视频不溢出 (保留) */
img, video { /* 应用于更广泛的图片/视频，而不仅仅是回复卡片内 */
  max-width: 100% !important;
  height: auto !important;
}


body {
  background-image: radial-gradient(at 40% 20%, rgba(229, 153, 87, 0.5) 0px, transparent 50%), radial-gradient(at 80% 0%, rgba(68, 143, 248, 0.5) 0px, transparent 40%), radial-gradient(at 0% 50%, rgba(248, 68, 83, 0.5) 0px, transparent 70%), radial-gradient(at 80% 50%, rgba(229, 87, 135, 0.5) 0px, transparent 70%), radial-gradient(at 0% 100%, rgba(200, 116, 200, 0.6) 0px, transparent 50%), radial-gradient(at 80% 100%, rgba(135, 87, 229, 0.5) 0px, transparent 50%), radial-gradient(at 0% 0%, rgba(239, 158, 78, 0.5) 0px, transparent 50%) !important;
  background-attachment: fixed !important;
}

/*--------标题栏--------*/
.d-header-wrap {
	position: relative !important;
}

.d-header {
	background-color: rgba(255, 255, 255, 0.5)!important;
  box-shadow: 0 1px 20px rgba(0, 0, 0, 0.05) !important
  border-bottom: 1px solid rgba(255, 255, 255, 0.3) !important;
}

/*--------主页--------*/
#main-outlet-wrapper {
	background: rgba(255, 255, 255, 0.5) !important;
	border: 1px solid rgba(255, 255, 255, 0.3) !important;
  margin-top: 40px !important;
  border-radius: 12px !important;
  width: 80% !important;
  overflow: hidden;
}

.anon .topic-list-main-link a.title:visited:not(.badge-notification), .anon .topic-list .main-link a.title:visited:not(.badge-notification), .topic-list .anon .main-link a.title:visited:not(.badge-notification) {
	color: #333 !important;
}

.discourse-tag {
	border-radius: 4px;
}

#list-area .show-more {
	top: -30px;
}

#list-area .show-more .alert {
	padding: 5px !important;
	font-size: 14px !important;
	display: block !important;
	background-color: rgba(0, 0, 0, 0.5) !important;
	border-radius: 8px !important;
	color: #fff !important;
	display: block !important;
	text-align: center;
}
.nav-pills > li a.active, .nav-pills > li button.active {
	color: #ca4e4e  !important;
}
/*--------帖子列表布局修改--------*/
/* 修改帖子列表项的布局 */
.topic-list {
  border-collapse: separate !important;
  border-spacing: 0 8px !important;
  padding: 0 20px;
}

.topic-list-item, tr {
	border-bottom: none !important;
}

.topic-list-main-link a.title, .latest-topic-list-item .main-link a.title, .topic-list .main-link a.title {
	font-size: 15px;
}

.topic-list .topic-excerpt {
	font-size: 13px !important;
}

.topic-list .link-bottom-line a.discourse-tag.box {
	font-size: 12px;
}
.badge-category__wrapper .badge-category__name {
	color: #fff !important;
	background: rgba(0, 0, 0, 0.3) !important;
	border-radius: 6px !important;
	font-size: 10px !important;
	padding: 0 6px !important;
	line-height: 2.0 !important;
}

.topic-list .link-bottom-line {
	font-size: var(--font-down-1);
	margin-top: 0 !important;
  gap: 0 .5em !important;
  line-height: inherit !important;
}

.topic-list-item {
  display: flex !important;
  padding: 12px !important;
  align-items: flex-start !important;
  background-color: rgba(255, 255, 255, 0.8) !important;
  border: 1px solid rgba(255, 255, 255, 0.4) !important;
  margin: 0 25px 15px 25px !important;
  border-radius: 8px !important;
  transition: all 0.2s ease !important;
  position: relative !important;border: 1px solid rgba(255, 255, 255, 0.8) !important;
}

.topic-list .posters {
	width: auto !important;
}

/* 隐藏原始表格结构 */
.topic-list-item td {
  border: none !important;
  padding: 0 !important;
}

/* 隐藏不需要的单元格 */
.topic-list-item td.posters-names,
.topic-list-item td.activity,
.topic-list-item td.age,
.topic-list-item td.views,
.topic-list-item td.posts-map,
.topic-list-item td.posts {
  display: none !important;
}

/* 显示并定位头像 - 确保在左侧 */
.topic-list-item td.posters {
	display: block !important;
	margin-right: 12px !important;
	position: absolute !important;
	left: 20px !important;
	top: 20px !important;
}

.topic-list-item td.posters a:first-child {
  display: block !important;
}

.topic-list-item td.posters a:not(:first-child) {
  display: none !important;
}

.topic-list-item img.avatar {
	width: 48px !important;
	border-radius: 10px !important;
	box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
	border: 2px solid rgba(0, 0, 0, 0.8) !important;
}

/* 创建右侧内容区域 */
.topic-list-item td.main-link {
  display: flex !important;
  flex-direction: column !important;
  flex: 1 !important;
  padding: 0 !important;
  margin-left: 70px !important; /* 为头像留出空间 */
  position: relative !important;
  line-height: 2;
}

/* 标题样式 */
.topic-list-item .main-link a.title {
	font-size: 15px !important;
	padding: 0 !important;
	font-weight: bold !important;
	color: #333 !important;
	display: block !important;
}

/* 隐藏原始元数据元素，防止它们在被移动到容器前显示在错误位置 */
.topic-list-item td.num.views,
.topic-list-item .topic-list-data.heatmap-low,
.topic-list-item td.num.posts,
.topic-list-item td.num.posts-map.posts,
.topic-list-item td.num.activity,
.topic-list-item td.activity.num.topic-list-data.age {
  visibility: hidden !important;
  position: absolute !important;
  top: -9999px !important;
  left: -9999px !important;
}

/* 当元素被移动到元数据容器后恢复可见性 */
.topic-metadata-container .topic-metadata-item {
  visibility: visible !important;
  position: static !important;
  top: auto !important;
  left: auto !important;
}

/* 通用图标样式 */
.topic-list-item .icon-before:before {
  margin-right: 4px !important;
  font-size: 14px !important;
  color: #333 !important;
  font-family: sans-serif !important;
  display: inline-block !important;
  width: 14px !important;
  height: 14px !important;
  background-size: contain !important;
  background-repeat: no-repeat !important;
  background-position: center !important;
  vertical-align: middle !important;
}

/* 元数据容器 */
.topic-list-item .topic-metadata-container {
  display: flex !important;
  flex-wrap: wrap !important;
  align-items: center !important;
  gap: 16px !important;
  position: relative !important;
  width: 100% !important;
  box-sizing: border-box !important;
  left: 0 !important;
  right: 0 !important;
}

/* 元数据项通用样式 */
.topic-list-item .topic-metadata-item {
  display: inline-flex !important;
  align-items: center !important;
  font-size: 13px !important;
  color: #666 !important;
  white-space: nowrap !important;
}

/* 显示作者名字 */
.topic-list-item .topic-author-name {
  display: inline-flex !important;
  align-items: center !important;
}

.topic-list-item .topic-author-name:before {
  content: "" !important;
  margin-right: 4px !important;
  width: 14px !important;
  height: 14px !important;
  display: inline-block !important;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23333'%3E%3Cpath d='M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z'/%3E%3C/svg%3E") !important;
  background-size: contain !important;
  background-repeat: no-repeat !important;
  background-position: center !important;
  vertical-align: middle !important;
}

/* 显示浏览量 */
.topic-list-item td.num.views {
  display: inline-flex !important;
  align-items: center !important;
}

.topic-list-item td.num.views:before {
  content: "" !important;
  margin-right: 4px !important;
  width: 14px !important;
  height: 14px !important;
  display: inline-block !important;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23333'%3E%3Cpath d='M12 4.5C7 4.5 2.73 7.61 1 12c1.73 4.39 6 7.5 11 7.5s9.27-3.11 11-7.5c-1.73-4.39-6-7.5-11-7.5zM12 17c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5zm0-8c-1.66 0-3 1.34-3 3s1.34 3 3 3 3-1.34 3-3-1.34-3-3-3z'/%3E%3C/svg%3E") !important;
  background-size: contain !important;
  background-repeat: no-repeat !important;
  background-position: center !important;
  vertical-align: middle !important;
}

/* 显示回复数 */
.topic-list-item td.num.posts {
  display: inline-flex !important;
  align-items: center !important;
}

.topic-list .num.posts a {
  padding: 0 !important;
}

.topic-list-item td.num.posts:before {
  content: "" !important;
  margin-right: 4px !important;
  width: 14px !important;
  height: 14px !important;
  display: inline-block !important;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23333'%3E%3Cpath d='M20 2H4c-1.1 0-2 .9-2 2v18l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2z'/%3E%3C/svg%3E") !important;
  background-size: contain !important;
  background-repeat: no-repeat !important;
  background-position: center !important;
  vertical-align: middle !important;
}

/* 显示最后回复时间 */
.topic-list-item td.last-post {
  display: inline-flex !important;
  align-items: center !important;
}

.topic-list-item td.last-post:before {
  content: "" !important;
  margin-right: 4px !important;
  width: 14px !important;
  height: 14px !important;
  display: inline-block !important;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23333'%3E%3Cpath d='M11.99 2C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2zM12 20c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8zm.5-13H11v6l5.25 3.15.75-1.23-4.5-2.67z'/%3E%3C/svg%3E") !important;
  background-size: contain !important;
  background-repeat: no-repeat !important;
  background-position: center !important;
  vertical-align: middle !important;
}

.topic-list .num.activity a {
  padding: 0 !important;
}

/* 显示创建/更新日期 */
.topic-list-item td.activity.num.topic-list-data.age {
  display: inline-flex !important;
  align-items: center !important;
  white-space: nowrap !important;
}

.topic-list-item td.activity.num.topic-list-data.age:before {
  content: "" !important;
  margin-right: 4px !important;
  width: 16px !important;
  height: 16px !important;
  min-width: 16px !important;
  min-height: 16px !important;
  max-width: 16px !important;
  max-height: 16px !important;
  display: inline-block !important;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='%23000000'%3E%3Cpath d='M12 2C6.5 2 2 6.5 2 12s4.5 10 10 10 10-4.5 10-10S17.5 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm.5-13H11v6l5.2 3.2.8-1.3-4.5-2.7V7z'/%3E%3C/svg%3E") !important;
  background-size: 16px 16px !important;
  background-repeat: no-repeat !important;
  background-position: center !important;
  vertical-align: middle !important;
  opacity: 1 !important;
  transform: scale(1) !important;
  zoom: 1 !important;
  -webkit-transform: scale(1) !important;
  -moz-transform: scale(1) !important;
  -ms-transform: scale(1) !important;
  -o-transform: scale(1) !important;
}

/* 显示分类 */
.topic-list-item td.category {
  display: inline-flex !important;
  align-items: center !important;
  position: absolute !important;
  right: 12px !important;
  bottom: 12px !important;
}

.topic-list-item td.category:before {
  content: "" !important;
  margin-right: 4px !important;
  width: 14px !important;
  height: 14px !important;
  display: inline-block !important;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23333'%3E%3Cpath d='M17.63 5.84C17.27 5.33 16.67 5 16 5L5 5.01C3.9 5.01 3 5.9 3 7v10c0 1.1.9 1.99 2 1.99L16 19c.67 0 1.27-.33 1.63-.84L22 12l-4.37-6.16z'/%3E%3C/svg%3E") !important;
  background-size: contain !important;
  background-repeat: no-repeat !important;
  background-position: center !important;
  vertical-align: middle !important;
}

.topic-list-item .discourse-tag {
  font-size: 11px !important;
  padding: 2px 6px !important;
  margin-right: 4px !important;
  border-radius: 4px !important;
  background-color: transition !important;
}

.discourse-tag.box {
	background-color: transparent !important;
}


/* 确保表头不受影响 */
#global-notice-alert-global-notice,
.svg-icon,
.svg-icon-title,
.topic-list-header {
  display: none !important;
}

.topic-statuses {
	display: none !important;
}

/* 确保所有图标不随滚动变化 */
.topic-list-item *:before,
.topic-list-item *::before {
  transform: scale(1) !important;
  zoom: 1 !important;
  -webkit-transform: scale(1) !important;
  -moz-transform: scale(1) !important;
  -ms-transform: scale(1) !important;
  -o-transform: scale(1) !important;
}

/* 确保时间图标正确显示且不随滚动变化 */
td.activity.num.topic-list-data.age:before,
td.activity.num.topic-list-data.age::before {
  content: "" !important;
  margin-right: 4px !important;
  width: 16px !important;
  height: 16px !important;
  min-width: 16px !important;
  min-height: 16px !important;
  max-width: 16px !important;
  max-height: 16px !important;
  display: inline-block !important;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='%23000000'%3E%3Cpath d='M12 2C6.5 2 2 6.5 2 12s4.5 10 10 10 10-4.5 10-10S17.5 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm.5-13H11v6l5.2 3.2.8-1.3-4.5-2.7V7z'/%3E%3C/svg%3E") !important;
  background-size: 16px 16px !important;
  background-repeat: no-repeat !important;
  background-position: center !important;
  vertical-align: middle !important;
  opacity: 1 !important;
  transform: scale(1) !important;
  zoom: 1 !important;
  -webkit-transform: scale(1) !important;
  -moz-transform: scale(1) !important;
  -ms-transform: scale(1) !important;
  -o-transform: scale(1) !important;
}

/* 确保元数据容器中的所有元素都有正确的样式 */
.topic-metadata-container .topic-metadata-item {
  margin-right: 0 !important;
  padding: 0 !important;
  position: static !important;
  left: auto !important;
  right: auto !important;
  bottom: auto !important;
  top: auto !important;
  display: inline-flex !important;
  align-items: center !important;
  font-size: 12px !important;
  color: #666 !important;
}

/* 添加平滑过渡效果 */
.topic-metadata-container {
  transition: opacity 0.2s ease !important;
}

/* 确保linux-do-beautify类应用后立即隐藏原始元素 */
.linux-do-beautify .topic-list-item td.num.views,
.linux-do-beautify .topic-list-item .topic-list-data.heatmap-low,
.linux-do-beautify .topic-list-item td.num.posts,
.linux-do-beautify .topic-list-item td.num.posts-map.posts,
.linux-do-beautify .topic-list-item td.num.activity,
.linux-do-beautify .topic-list-item td.activity.num.topic-list-data.age {
  visibility: hidden !important;
  position: absolute !important;
  top: -9999px !important;
  left: -9999px !important;
}

/* 为各种图标添加样式 */
.topic-metadata-item .views-icon,
.topic-metadata-item .heatmap-icon,
.topic-metadata-item .posts-icon,
.topic-metadata-item .activity-icon,
.topic-metadata-item .age-icon {
  display: inline-block !important;
  width: 14px !important;
  height: 14px !important;
  margin-right: 4px !important;
  background-size: contain !important;
  background-repeat: no-repeat !important;
  background-position: center !important;
  vertical-align: middle !important;
}

.fk-d-menu__inner-content {
	background-color: transparent;
	border-radius: 12px !important;
  background: rgba(255, 255, 255, 0.8) !important;
	box-shadow: 0 1px 40px rgba(0, 0, 0, 0.1) !important;
	border: 1px solid rgba(255, 255, 255, 0.4) !important;
	backdrop-filter: blur(20px) saturate(180%) !important;
}

.user-card .badge-section .user-badge, .user-card .badge-section .more-user-badges a {
	background: rgba(0, 0, 0, 0.05) !important;
	border-radius: 8px !important;
	font-size: 13px !important;
  border: none !important;
}

.user-card .metadata, .group-card .metadata {
	font-size: 14px  !important;
}

.btn.btn-icon-text.btn-default {
	font-size: 14px !important;
	background: rgba(0, 0, 0, 0.05) !important;
	border-radius: 10px !important;
}

.user-card, .group-card {
	background: transparent !important;
}

.user-card-avatar .avatar-flair.rounded, .user-profile-avatar .avatar-flair.rounded {
	display: none  !important;
}

.user-card .card-content, .group-card .card-content {
	padding: 30px  !important;
	background: transparent  !important;
}

/* 浏览量图标 */
.topic-metadata-item .views-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23333'%3E%3Cpath d='M12 4.5C7 4.5 2.73 7.61 1 12c1.73 4.39 6 7.5 11 7.5s9.27-3.11 11-7.5c-1.73-4.39-6-7.5-11-7.5zM12 17c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5zm0-8c-1.66 0-3 1.34-3 3s1.34 3 3 3 3-1.34 3-3-1.34-3-3-3z'/%3E%3C/svg%3E") !important;
}

/* 回复数图标 */
.topic-metadata-item .posts-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23333'%3E%3Cpath d='M20 2H4c-1.1 0-2 .9-2 2v18l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2z'/%3E%3C/svg%3E") !important;
}

/* 活动时间图标 */
.topic-metadata-item .activity-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23333'%3E%3Cpath d='M11.99 2C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2zM12 20c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8zm.5-13H11v6l5.25 3.15.75-1.23-4.5-2.67z'/%3E%3C/svg%3E") !important;
}

/* 创建时间图标 */
.topic-metadata-item .age-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='%23000000'%3E%3Cpath d='M12 2C6.5 2 2 6.5 2 12s4.5 10 10 10 10-4.5 10-10S17.5 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm.5-13H11v6l5.2 3.2.8-1.3-4.5-2.7V7z'/%3E%3C/svg%3E") !important;
}

/* 热度图标 */
.topic-metadata-item .heatmap-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23333'%3E%3Cpath d='M13.5.67s.74 2.65.74 4.8c0 2.06-1.35 3.73-3.41 3.73-2.07 0-3.63-1.67-3.63-3.73l.03-.36C5.21 7.51 4 10.62 4 14c0 4.42 3.58 8 8 8s8-3.58 8-8C20 8.61 17.41 3.8 13.5.67zM11.71 19c-1.78 0-3.22-1.4-3.22-3.14 0-1.62 1.05-2.76 2.81-3.12 1.77-.36 3.6-1.21 4.62-2.58.39 1.29.59 2.65.59 4.04 0 2.65-2.15 4.8-4.8 4.8z'/%3E%3C/svg%3E") !important;
}

/*--------- 内页 ----------*/
#topic-title .title-wrapper {
	padding: 0 30px;
}

.topic-category {
	margin: 10px 0;
}

.discourse-tag.box {
	font-size: 12px;
}

.container.posts {
	grid-template-columns: 100% 25% !important;
  padding: 30px;
}

article[id^="post_"] img.avatar {
	border: 2px solid rgba(0, 0, 0, 0.8) !important;
}

body .reply-to-tab img.avatar {
	border: 1px solid rgba(0, 0, 0, 0.8) !important;
	border-radius: 5px !important;
  margin: 0 10px;
}

.topic-body {
  width: 90% !important;
	border-top: none !important;
}

.topic-body .cooked {
	padding: 0 var(--topic-body-width-padding) !important;
	font-size: 15px !important;
}

.topic-map__contents .topic-map__stats.--single-stat button span,
.post-info a, .post-info svg {
	font-size: 12px !important;
	color: var(--tertiary) !important;
}

.topic-map.--op {
	border-top: none !important;
}

.topic-map__stats {
	font-size: 13px;
}

.topic-status-info, .topic-timer-info {
	border-top: none !important;
}

.cooked img:not(.thumbnail, .ytp-thumbnail-image, .emoji), .d-editor-preview img:not(.thumbnail, .ytp-thumbnail-image, .emoji) {
	border-radius: 12px !important;
}

.reply-to-tab {
	display: none !important;

}.topic-meta-data .post-infos {
	align-items: end !important;
  font-size: 13px;
}

aside.quote .title {
	color: inherit !important;
	border-left: 0 !important;
	background-color: transparent !important;
	user-select: none;
}

blockquote {
  margin: 0 !important;
	border-left: none !important;
	background-color: transparent !important;
}

aside.quote {
	margin-bottom: 10px !important;
	padding: 8px 12px !important;
	background-color: rgba(0, 0, 0, 0.05)!important;
	border-left: 0 !important;
	border-radius: 10px !important;
	font-size: 14px !important;
}

.reply-quote {
    margin-bottom: 10px !important;
    padding: 8px 12px;
    background-color: rgba(0, 0, 0, 0.05) !important;
    border-left: 0 !important;
    border-radius: 10px;
    font-size: 14px;
}

.cooked img:not(.thumbnail, .ytp-thumbnail-image, .emoji), .d-editor-preview img:not(.thumbnail, .ytp-thumbnail-image, .emoji) {
	border-radius: 6px !important;
}

.names span.first > a {
	color: #000 !important;
}

.second.username > a {
	color: #fff;
	background: rgba(0, 0, 0, .1);
	border-radius: 6px !important;
	font-size: 12px;
	padding: 0 6px;
}

.small-action .small-action-desc {
	box-sizing: border-box;
	display: block !important;
	flex-wrap: wrap;
	color: #000 !important;
	padding: 1em 0 !important;
	width: 100% !important;
	min-width: 0;
	border-top: 1px solid #fff !important;
	text-align: center;
}

.small-action .topic-avatar {
	display: none !important;
}

.lightbox-wrapper .lightbox {
    outline: 5px solid rgba(0, 0, 0, 0.1) !important;
    border-radius: 12px !important;
}

aside.onebox {
	border-radius: 12px !important;
}

.topic-avatar .avatar-flair.rounded, .avatar-flair-preview .avatar-flair.rounded, .collapsed-info .user-profile-avatar .avatar-flair.rounded, .user-image .avatar-flair.rounded, .latest-topic-list-item .avatar-flair.rounded {
	display: none !important;
}

.topic-map {
	max-width: 98% !important;
}



.topic-avatar {
	width: 48px !important;
}

.fk-d-menu {
	margin: 10px;
}

.user-card .names__secondary, .user-card [class*="metadata__"], .group-card .names__secondary, .group-card [class*="metadata__"],
.user-card .card-row:not(.first-row), .group-card .card-row:not(.first-row) ,
.user-card .card-content .bio, .group-card .card-content .bio {
	font-size: 14px !important;
}

#user-card img.avatar,
#user-card img.prefix-image {
  border-radius: 16px !important;
	border: 4px solid rgba(0, 0, 0, 0.8) !important;
}

.user-card .first-row .names, .group-card .first-row .names {
	padding-left: 20px !important;
}

.topic-meta-data .user-status-message-wrap img.emoji {

	display: none;
}
/* 楼主标签样式 - 使用伪元素 */
div.topic-avatar::after {
    content: "";
    display: none;
}

div.topic-owner .topic-avatar::after {
	content: "楼主";
	display: block;
	background-color: #e74c3c;
	color: white;
	font-size: 10px;
	font-weight: bold;
	padding: 2px 6px;
	border-radius: 6px;
	text-align: center;
	width: 20px;
	margin: 15px auto 0 auto;
}

.topic-map .--users-summary {
    gap: 0.5em !important;
}

.topic-map__contents .topic-map__stats.--single-stat button span, .post-info a, .post-info svg {
	font-size: 12px !important;
	color: rgba(0, 0, 0, 0.5) !important;
	padding: 5px 0 !important;
	display: block !important;
}

.topic-map__contents .topic-map__stats .fk-d-menu__trigger .number {
	color: #000 !important;
}

.topic-map .--users-summary .avatar {
	border-radius: 8px !important;
	border: 2px solid rgba(0, 0, 0, 0.8) !important;
}

.sidebar-section-link-wrapper .sidebar-section-link--active, .sidebar-section-link-wrapper .sidebar-section-link.active {
	background: rgba(0, 0, 0, 0.05) !important;
	border-radius: 8px !important;
}

/* 隐藏原始的楼主标识 */
div.topic-owner .topic-body .contents > .cooked::after {
    display: none !important;
}

code {
	background: rgba(0, 0, 0, 0.07)!important;
	font-size: 14px;
	border-radius: 10px !important;
}

.more-topics__container .topic-list-item td.main-link {
	padding: 0 10px !important;
  margin-left: 0 !important;
}

/* --------登录页----------*/
input[type="text"], input[type="password"], input[type="datetime"], input[type="datetime-local"], input[type="date"], input[type="month"], input[type="time"], input[type="week"], input[type="number"], input[type="email"], input[type="url"], input[type="search"], input[type="tel"], input[type="color"] {
	border-radius: 8px !important;
	background: rgba(255, 255, 255, 0.5) !important;
	border: 1px solid rgba(255, 255, 255, 0.5) !important;
	box-shadow: 0 0px 20px rgba(0, 0, 0, 0.05) !important;
}

.login-fullpage #login-buttons .btn-social, .signup-fullpage #login-buttons .btn-social, .invites-show #login-buttons .btn-social, .password-reset-page #login-buttons .btn-social {
	padding: .75em .77em;
	border-radius: 8px !important;
	border: 1px solid rgba(255, 255, 255, 0.5) !important;
	box-shadow: 0 0px 20px rgba(0, 0, 0, 0.05), inset 0px 0px 30px 10px rgba(255, 255, 255, 0.3) !important;
	background: rgba(255, 255, 255, 0.5) !important;
}

.login-fullpage .login-page-cta__existing-account::before, .login-fullpage .login-page-cta__no-account-yet::before, .login-fullpage .signup-page-cta__existing-account::before, .login-fullpage .signup-page-cta__no-account-yet::before, .signup-fullpage .login-page-cta__existing-account::before, .signup-fullpage .login-page-cta__no-account-yet::before, .signup-fullpage .signup-page-cta__existing-account::before, .signup-fullpage .signup-page-cta__no-account-yet::before, .invites-show .login-page-cta__existing-account::before, .invites-show .login-page-cta__no-account-yet::before, .invites-show .signup-page-cta__existing-account::before, .invites-show .signup-page-cta__no-account-yet::before {
	background-color: transparent !important;
}

.btn-primary {
	border-radius: 8px !important;
}

        `);
    }

    // ========== 工具函数 ==========
    function getFloorNumber(element) {
        if (element.id?.includes('_')) {
            return element.id.split('_')[1];
        }
        const postId = element.getAttribute('data-post-id');
        return postId ? postId.replace('top--', '') : '';
    }

    function cachePostContent(postId, content, author, floorNumber, avatarSrc) {
        if (!postId || !content) return;
        postCache.set(postId, {
            content,
            author: author || '未知用户',
            floorNumber: floorNumber || '',
            avatarSrc: avatarSrc || '',
            timestamp: Date.now()
        });
    }

    // ========== 统一的楼层号处理 ==========
    function updateFloorNumbers() {
        document.querySelectorAll('article[id^="post_"]').forEach(function (post) {
            if (!post.hasAttribute('data-floor-number')) { // 检查是否已经添加了楼层号
                const floorNum = getFloorNumber(post);
                if (floorNum) {
                    post.setAttribute('data-floor-number', floorNum);
                    log(`设置楼层号: ${post.id} -> #${floorNum}`);
                }
            }
        });
    }

    // ========== 核心处理逻辑 ==========
    function processPost(post) {
        try {
            if (!post?.isConnected || post.hasAttribute('data-quote-added')) return;
            post.setAttribute('data-quote-added', 'true');

            const replyTab = post.querySelector('.reply-to-tab');
            const replyToPostId = replyTab?.getAttribute('aria-controls');
            if (!replyToPostId) return;

            log(`处理帖子 ${post.id} -> 目标 ${replyToPostId}`);

            // 1. 检查缓存
            const cachedPost = postCache.get(replyToPostId);
            if (cachedPost) {
                addQuoteToPost(post, cachedPost);
                return;
            }

            // 2. 查找嵌入内容
            const embeddedPosts = post.querySelector('.embedded-posts.top');
            if (embeddedPosts) {
                processEmbeddedReply(embeddedPosts, post, replyToPostId);
                return;
            }

            // 3. 查找页面上的父帖子
            const parentPost = findParentPost(replyToPostId);
            if (parentPost) {
                const postData = extractPostData(parentPost, replyToPostId);
                if (postData) {
                    cachePostContent(replyToPostId, ...Object.values(postData));
                    addQuoteToPost(post, postData);
                    return;
                }
            }

            // 4. 最后尝试触发加载
            if (!window.isScrolling) {
                fetchParentPostLegacy(replyToPostId, post);
            } else {
                post.removeAttribute('data-quote-added');
            }

        } catch (error) {
            log('处理帖子时出错:', error);
            post?.removeAttribute('data-quote-added');
        }
    }

    // ========== 辅助函数 ==========
    function findParentPost(replyToPostId) {
        const postId = replyToPostId.replace('top--', '');
        return document.querySelector(`article[data-post-id="${replyToPostId}"], #post_${postId}`);
    }

    function extractPostData(parentPost, replyToPostId) {
        const content = parentPost.querySelector('.cooked');
        const author = parentPost.querySelector('.names .username');
        const avatar = parentPost.querySelector('.topic-avatar img.avatar');

        if (!content || !author) return null;

        return {
            content: content.innerHTML,
            author: author.textContent.trim(),
            floorNumber: getFloorNumber(parentPost) || replyToPostId.replace('top--', ''),
            avatarSrc: avatar?.getAttribute('src') || ''
        };
    }

    // ========== 处理嵌入内容 ==========
    function processEmbeddedReply(embeddedPosts, post, replyToPostId) {
        try {
            embeddedPosts.style.display = 'none';

            const postData = extractPostData(embeddedPosts, replyToPostId);
            if (!postData) {
                log('嵌入帖子缺少必要元素');
                return;
            }

            // 获取楼层号
            const dataPostId = embeddedPosts.getAttribute('data-post-id');
            if (dataPostId) {
                postData.floorNumber = dataPostId.replace('top--', '');
            }

            if (replyToPostId) {
                cachePostContent(replyToPostId, ...Object.values(postData));
            }

            addQuoteToPost(post, postData);

            embeddedPosts.classList.add('processed');
            post.setAttribute('data-quote-processed', 'true');

            const replyTab = post.querySelector('.reply-to-tab');
            if (replyTab) replyTab.style.display = 'none';

        } catch (error) {
            log('处理嵌入回复时出错:', error);
        }
    }

    // ========== 模拟点击方法 ==========
    function fetchParentPostLegacy(replyToPostId, post) {
        try {
            const replyTab = post.querySelector('.reply-to-tab');
            if (!replyTab?.isConnected) return;

            // 修复Firefox兼容性问题 - 使用更兼容的事件创建方式
            let clickEvent;
            try {
                // 尝试标准方式
                clickEvent = new MouseEvent('click', {
                    bubbles: true,
                    cancelable: true
                });
            } catch (e) {
                // Firefox兼容性回退
                clickEvent = document.createEvent('MouseEvent');
                clickEvent.initMouseEvent('click', true, true, window, 0, 0, 0, 0, 0, false, false, false, false, 0, null);
            }

            replyTab.dispatchEvent(clickEvent);

            let attempts = 0;
            const checkForEmbeddedPosts = () => {
                if (++attempts > CONFIG.MAX_FETCH_ATTEMPTS || !post?.isConnected) {
                    post?.removeAttribute('data-quote-added');
                    return;
                }

                const embeddedPosts = post.querySelector('.embedded-posts.top');
                if (embeddedPosts?.querySelector('.cooked')?.innerHTML.trim()) {
                    embeddedPosts.style.display = 'none';
                    processEmbeddedReply(embeddedPosts, post, replyToPostId);
                } else {
                    setTimeout(checkForEmbeddedPosts, CONFIG.FETCH_DELAY);
                }
            };

            checkForEmbeddedPosts();
        } catch (error) {
            log('模拟点击方法失败:', error);
            post?.removeAttribute('data-quote-added');
        }
    }

    // ========== 创建引用 ==========
    function addQuoteToPost(post, { content, author, floorNumber, avatarSrc }) {
        try {
            const regularContents = post.querySelector('.regular.contents');
            if (!regularContents || regularContents.hasAttribute('data-quote-added')) return;

            regularContents.setAttribute('data-quote-added', 'true');

            // 创建引用元素
            const quoteElement = document.createElement('blockquote');
            quoteElement.className = 'optimized-reply-quote';

            const headerContent = avatarSrc
                ? `<img src="${avatarSrc}" class="quote-avatar" alt="${author}" />
                   <span class="quote-author">${author}</span>`
                : floorNumber ? `#${floorNumber} ${author}` : `引用自 ${author}:`;

            quoteElement.innerHTML = `
                <div class="quote-header">${headerContent}</div>
                <div class="quote-content">${content}</div>
            `;

            const postContent = regularContents.querySelector('.cooked') || regularContents;
            postContent.insertBefore(quoteElement, postContent.firstChild);

            post.setAttribute('data-quote-processed', 'true');

            const replyTab = post.querySelector('.reply-to-tab');
            if (replyTab) replyTab.style.display = 'none';

            log(`引用创建成功: ${post.id} -> ${author} #${floorNumber}`);

        } catch (error) {
            log('添加引用时出错:', error);
        }
    }

    // ========== 美化功能：添加元数据容器并整理元数据元素 ==========
    function organizeTopicMetadata() {
        const topicItems = document.querySelectorAll('.topic-list-item:not([data-metadata-processed="true"])');
        log(`找到${topicItems.length}个未处理的topic-list-item`);

        topicItems.forEach(function(item) {
            // 双重检查是否已经处理过，避免重复处理
            if (item.querySelector('.topic-metadata-container') || item.dataset.metadataProcessed === 'true') {
                log('跳过已处理的元素');
                item.dataset.metadataProcessed = 'true'; // 确保标记为已处理
                return;
            }

            const mainLink = item.querySelector('td.main-link');
            if (!mainLink) {
                log('未找到main-link元素，跳过');
                return;
            }

            // 再次检查mainLink中是否已有元数据容器
            if (mainLink.querySelector('.topic-metadata-container')) {
                log('main-link中已有元数据容器，跳过');
                item.dataset.metadataProcessed = 'true';
                return;
            }

            // 使用DocumentFragment批量构建DOM结构
            const fragment = document.createDocumentFragment();

            // 创建元数据容器
            const metadataContainer = document.createElement('div');
            metadataContainer.className = 'topic-metadata-container';
            // 设置样式，但不添加到DOM，等所有子元素准备好后一次性添加
            metadataContainer.style.opacity = '1';
            metadataContainer.style.transition = 'opacity 0.2s ease';

            let hasMetadata = false; // 标记是否添加了任何元数据

            // 1. 添加作者名字
            let authorName = '';
            const userLink = item.querySelector('a[data-user-card]');
            if (userLink) {
                authorName = userLink.getAttribute('data-user-card');
                if (authorName) {
                    const authorElement = document.createElement('div');
                    authorElement.className = 'topic-author-name topic-metadata-item';
                    authorElement.textContent = authorName;
                    metadataContainer.appendChild(authorElement);
                    hasMetadata = true;
                    log(`添加作者: ${authorName}`);
                }
            }

            // 2. 移动浏览量到元数据容器
            const viewsElement = item.querySelector('td.num.views');
            if (viewsElement && viewsElement.textContent.trim()) {
                const viewsMetadata = document.createElement('div');
                viewsMetadata.className = 'topic-metadata-item views-metadata';
                // 直接设置最终HTML内容，避免多次修改
                viewsMetadata.innerHTML = '<span class="views-icon"></span>' + viewsElement.textContent;
                metadataContainer.appendChild(viewsMetadata);
                hasMetadata = true;
                log(`添加浏览量: ${viewsElement.textContent}`);
            }

            // 3. 移动热度指标到元数据容器
            const heatmapElement = item.querySelector('.topic-list-data.heatmap-low');
            if (heatmapElement && heatmapElement.textContent.trim()) {
                const heatmapMetadata = document.createElement('div');
                heatmapMetadata.className = 'topic-metadata-item heatmap-metadata';
                heatmapMetadata.innerHTML = '<span class="heatmap-icon"></span>' + heatmapElement.textContent;
                metadataContainer.appendChild(heatmapMetadata);
                hasMetadata = true;
                log(`添加热度: ${heatmapElement.textContent}`);
            }

            // 4. 移动回复数到元数据容器
            const postsElement = item.querySelector('td.num.posts, td.num.posts-map.posts');
            if (postsElement && postsElement.textContent.trim()) {
                const postsMetadata = document.createElement('div');
                postsMetadata.className = 'topic-metadata-item posts-metadata';
                postsMetadata.innerHTML = '<span class="posts-icon"></span>' + postsElement.textContent;
                metadataContainer.appendChild(postsMetadata);
                hasMetadata = true;
                log(`添加回复数: ${postsElement.textContent}`);
            }

            // 5. 移动时间到元数据容器 - 优先使用创建时间(age)，如果没有则使用活动时间(activity)
            // 注意：只添加一个时间元素，避免重复
            const ageElement = item.querySelector('td.activity.num.topic-list-data.age');
            const activityElement = item.querySelector('td.num.activity');

            // 优先使用创建时间
            if (ageElement && ageElement.textContent.trim()) {
                const timeMetadata = document.createElement('div');
                timeMetadata.className = 'topic-metadata-item age-metadata';
                timeMetadata.innerHTML = '<span class="age-icon"></span>' + ageElement.textContent;
                metadataContainer.appendChild(timeMetadata);
                hasMetadata = true;
                log(`添加创建时间: ${ageElement.textContent}`);
            }
            // 如果没有创建时间，则使用活动时间
            else if (activityElement && activityElement.textContent.trim()) {
                const timeMetadata = document.createElement('div');
                timeMetadata.className = 'topic-metadata-item activity-metadata';
                timeMetadata.innerHTML = '<span class="activity-icon"></span>' + activityElement.textContent;
                metadataContainer.appendChild(timeMetadata);
                hasMetadata = true;
                log(`添加活动时间: ${activityElement.textContent}`);
            }

            // 只有在确实添加了元数据时才将容器添加到DOM
            if (hasMetadata) {
                // 将准备好的容器添加到fragment
                fragment.appendChild(metadataContainer);

                // 一次性将fragment添加到DOM
                mainLink.appendChild(fragment);

                // 标记该项已处理
                item.dataset.metadataProcessed = 'true';
                log('元数据容器创建成功');
            } else {
                log('未找到任何元数据，跳过创建容器');
                // 即使没有元数据也标记为已处理，避免重复尝试
                item.dataset.metadataProcessed = 'true';
            }
        });
    }

    // ========== 美化功能：延迟重试机制 ==========
    function organizeTopicMetadataWithRetry() {
        // 立即执行一次
        organizeTopicMetadata();

        // 如果还有未处理的元素，延迟重试
        setTimeout(() => {
            const unprocessedItems = document.querySelectorAll('.topic-list-item:not([data-metadata-processed="true"])');
            if (unprocessedItems.length > 0) {
                log(`延迟重试: 发现${unprocessedItems.length}个未处理的列表项`);
                organizeTopicMetadata();
            }
        }, 500);

        // 再次延迟重试
        setTimeout(() => {
            const unprocessedItems = document.querySelectorAll('.topic-list-item:not([data-metadata-processed="true"])');
            if (unprocessedItems.length > 0) {
                log(`第二次延迟重试: 发现${unprocessedItems.length}个未处理的列表项`);
                organizeTopicMetadata();
            }
        }, 1000);
    }

    // ========== 页面切换检测 ==========
    function handlePageChange() {
        log('检测到页面变化，重置美化状态');
        // 清除所有已处理标记，重新处理
        const allTopicItems = document.querySelectorAll('.topic-list-item[data-metadata-processed="true"]');
        allTopicItems.forEach(item => {
            item.removeAttribute('data-metadata-processed');
        });
        // 延迟处理，给页面时间加载
        setTimeout(() => {
            organizeTopicMetadataWithRetry();
        }, 300);
    }

    // ========== 美化功能：添加楼主标识 ==========
    function addTopicOwnerClass() {
        // 检查当前是否在帖子详情页
        if (!window.location.pathname.startsWith('/t/')) {
            return;
        }

        log('添加楼主标识...');

        // 获取第一个帖子（通常是楼主帖）
        const firstPost = document.querySelector('#post_1');
        if (!firstPost) {
            log('未找到第一个帖子');
            return;
        }

        // 检查是否已有topic-owner类
        if (firstPost.classList.contains('topic-owner')) {
            log('第一个帖子已有topic-owner类');
            return;
        }

        // 获取第一个帖子的作者
        const firstPostAuthor = firstPost.querySelector('[data-user-card]');
        if (!firstPostAuthor) {
            log('未找到第一个帖子的作者信息');
            return;
        }

        const firstPostUsername = firstPostAuthor.getAttribute('data-user-card');

        // 获取主题作者（从主题元数据中获取）
        const topicAuthor = document.querySelector('.topic-meta-data [data-user-card]');
        if (!topicAuthor) {
            log('未找到主题作者信息');
            return;
        }

        const topicUsername = topicAuthor.getAttribute('data-user-card');

        // 比较作者是否一致，如果一致则添加topic-owner类
        if (firstPostUsername === topicUsername) {
            firstPost.classList.add('topic-owner');
            log(`为第一个帖子添加topic-owner类 (作者: ${firstPostUsername})`);
        } else {
            log(`第一个帖子作者 (${firstPostUsername}) 与主题作者 (${topicUsername}) 不一致`);
        }
    }

    // ========== 批量处理 ==========
    function processAllPosts() {
        const now = Date.now();
        if (isProcessing || (now - lastProcessTime < (isFirefox ? 150 : 100))) return;

        isProcessing = true;
        lastProcessTime = now;

        try {
            const allPosts = Array.from(document.querySelectorAll('article[id^="post_"]:not([data-quote-added])'));
            if (!allPosts.length) {
                isProcessing = false;
                return;
            }

            const postsToProcess = allPosts.slice(0, CONFIG.MAX_POSTS_PER_BATCH);
            log(`处理帖子批次: ${postsToProcess.length}/${allPosts.length}`);

            let index = 0;
            const processNext = () => {
                if (index >= postsToProcess.length) {
                    isProcessing = false;
                    return;
                }

                const endIndex = Math.min(index + CONFIG.BATCH_SIZE, postsToProcess.length);

                for (let i = index; i < endIndex; i++) {
                    const post = postsToProcess[i];
                    if (post?.isConnected) processPost(post);
                }

                index = endIndex;

                if (index < postsToProcess.length) {
                    if (isFirefox) {
                        setTimeout(processNext, CONFIG.PROCESS_DELAY);
                    } else {
                        rafId = requestAnimationFrame(processNext);
                    }
                } else {
                    isProcessing = false;
                }
            };

            isFirefox ? setTimeout(processNext, 0) : requestAnimationFrame(processNext);
        } catch (error) {
            log('处理帖子批次出错:', error);
            isProcessing = false;
        }
    }

    // ========== 节流函数 ==========
    function throttle(func, delay) {
        let lastCall = 0;
        let timeoutId = null;

        return function(...args) {
            const now = Date.now();
            const remaining = delay - (now - lastCall);

            if (remaining <= 0) {
                clearTimeout(timeoutId);
                timeoutId = null;
                lastCall = now;
                func.apply(this, args);
            } else if (!timeoutId) {
                timeoutId = setTimeout(() => {
                    lastCall = Date.now();
                    timeoutId = null;
                    func.apply(this, args);
                }, remaining);
            }
        };
    }

    // ========== 防抖动函数 ==========
    function debounce(func, wait) {
        let timeout;
        let isExecuting = false; // 添加执行锁定标志

        return function() {
            // 如果正在执行，直接返回，避免重复执行
            if (isExecuting) return;

            const context = this;
            const args = arguments;

            // 清除之前的定时器
            clearTimeout(timeout);

            timeout = setTimeout(() => {
                // 设置执行锁定
                isExecuting = true;

                try {
                    // 执行函数
                    func.apply(context, args);
                } finally {
                    // 无论执行成功还是失败，都解除锁定
                    setTimeout(() => {
                        isExecuting = false;
                    }, 50); // 短暂延迟解锁，防止极端情况下的重复触发
                }
            }, wait);
        };
    }

    // ========== DOM观察器 ==========
    function initObserver() {
        // 回复重构的节流处理
        const throttledReplyHandler = throttle((mutations) => {
            const hasNewPosts = mutations.some(mutation =>
                mutation.type === 'childList' &&
                Array.from(mutation.addedNodes).some(node =>
                    node.nodeType === Node.ELEMENT_NODE &&
                    (node.matches?.('article[id^="post_"]') ||
                     node.querySelector?.('article[id^="post_"]'))
                )
            );

            if (hasNewPosts) {
                log('检测到新帖子');
                updateFloorNumbers();
                processAllPosts();
            }
        }, CONFIG.THROTTLE_DELAY);

        // 美化功能的防抖处理
        const debouncedOrganizeMetadata = debounce(function() {
            log('执行防抖动的元数据组织...');

            // 检测是否是页面切换（通过URL变化或主要内容变化判断）
            const currentPath = window.location.pathname;
            if (window.lastPath && window.lastPath !== currentPath) {
                log('检测到路径变化，触发页面切换处理');
                handlePageChange();
                window.lastPath = currentPath;
                return;
            }
            window.lastPath = currentPath;

            // 使用带重试的元数据组织
            organizeTopicMetadataWithRetry();

            // 在DOM变化时也尝试添加楼主标识
            addTopicOwnerClass();
        }, 150); // 增加延迟时间，给DOM更多时间稳定

        // 创建单一的观察器，同时处理两种功能
        const observer = new MutationObserver(function(mutations) {
            // 首先处理回复重构功能（优先级更高）
            throttledReplyHandler(mutations);

            // 然后检查美化功能
            // 检查是否有未处理的元素
            const checkUnprocessedItems = () => {
                const unprocessedItems = document.querySelectorAll('.topic-list-item:not([data-metadata-processed="true"])');
                return unprocessedItems.length > 0;
            };

            // 使用更精确的检查，减少不必要的处理
            let shouldCheckMetadata = false;

            // 快速检查是否有相关变化
            for (const mutation of mutations) {
                if (mutation.type !== 'childList' || mutation.addedNodes.length === 0) continue;

                // 检查是否添加了新的DOM元素
                const hasNewElements = Array.from(mutation.addedNodes).some(node =>
                    node.nodeType === Node.ELEMENT_NODE);

                if (hasNewElements) {
                    shouldCheckMetadata = true;
                    break;
                }
            }

            // 只有在确实需要检查时才执行检查
            let hasUnprocessedItems = false;

            if (shouldCheckMetadata) {
                hasUnprocessedItems = checkUnprocessedItems();
            }

            // 使用防抖动函数处理变化
            if (hasUnprocessedItems) {
                log('MutationObserver检测到未处理的列表项');
                debouncedOrganizeMetadata();
            }
        });

        const mainContent = document.querySelector('#main-outlet') || document.body;

        if (mainContent) {
            observer.observe(mainContent, {
                childList: true,
                subtree: true,
                attributes: false,
                characterData: false
            });
        }
    }

    // ========== 初始化 ==========
    function init() {
        log(`${isFirefox ? 'Firefox' : 'Chrome'}合并优化版开始初始化`);

        // 初始化样式
        initStyles();
        initBeautifyStyles();

        // 初始化页面路径跟踪
        window.lastPath = window.location.pathname;

        // 添加美化类到body
        document.addEventListener('DOMContentLoaded', function() {
            document.body.classList.add('linux-do-beautify');
        });

        window.isScrolling = false;
        initObserver();

        // 统一的初始化处理
        const initProcess = () => {
            updateFloorNumbers();
            organizeTopicMetadataWithRetry(); // 使用带重试的美化功能
            addTopicOwnerClass(); // 美化功能
            setTimeout(() => {
                processAllPosts();
            }, CONFIG.PROCESS_DELAY);
        };

        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', initProcess);
        } else {
            initProcess();
        }

        window.addEventListener('load', () => {
            updateFloorNumbers();
            organizeTopicMetadataWithRetry(); // 使用带重试的美化功能
            addTopicOwnerClass(); // 美化功能
            processAllPosts();
        });

        // 页面切换时的处理（监听历史记录变化）
        window.addEventListener('popstate', () => {
            log('检测到历史记录变化');
            setTimeout(() => {
                handlePageChange();
            }, 100);
        });

        // 监听路径变化（用于单页应用导航）
        let lastUrl = location.href;
        new MutationObserver(() => {
            const url = location.href;
            if (url !== lastUrl) {
                lastUrl = url;
                log('检测到URL变化');
                setTimeout(() => {
                    handlePageChange();
                }, 200);
            }
        }).observe(document, {subtree: true, childList: true});

        // DOMContentLoaded事件处理美化功能
        document.addEventListener('DOMContentLoaded', function() {
            log('linux.do美化脚本已加载，开始执行...');

            // 立即添加CSS类到body，以便尽早应用样式
            document.body.classList.add('linux-do-beautify');

            // 立即执行一次，尽早处理页面元素
            log('立即执行初始化操作...');
            organizeTopicMetadataWithRetry();
            addTopicOwnerClass();

            // 在DOM完全加载后执行一次，处理可能的动态内容
            setTimeout(function() {
                log('DOM加载后执行...');

                // 检查是否有新的未处理的topic-list-item
                const unprocessedItems = document.querySelectorAll('.topic-list-item:not([data-metadata-processed="true"])');
                if (unprocessedItems.length > 0) {
                    log('发现未处理的列表项，进行处理...');
                    organizeTopicMetadataWithRetry();
                }

                // 再次尝试添加楼主标识（以防DOM还未完全加载）
                addTopicOwnerClass();
            }, 300);
        });

        // 滚动事件处理
        const scrollThrottle = isFirefox ? 100 : 50;
        const scrollEndDelay = isFirefox ? 300 : 200;

        window.addEventListener('scroll', throttle(() => {
            window.isScrolling = true;
            updateFloorNumbers();

            clearTimeout(window.scrollEndTimer);
            window.scrollEndTimer = setTimeout(() => {
                window.isScrolling = false;
                processAllPosts();
            }, scrollEndDelay);
        }, scrollThrottle), { passive: true });

        // 定期维护
        setInterval(() => {
            // 清理过期缓存
            const now = Date.now();
            for (const [key, value] of postCache.entries()) {
                if (now - value.timestamp > CONFIG.CACHE_TTL) {
                    postCache.delete(key);
                }
            }
            // 保护楼层号
            updateFloorNumbers();
        }, CONFIG.CACHE_CLEAN_INTERVAL);

        window.addEventListener('beforeunload', () => {
            if (rafId) cancelAnimationFrame(rafId);
        });

        log(`${isFirefox ? 'Firefox' : 'Chrome'}合并优化版初始化完成`);
    }

    // ========== 启动 ==========
    init();

})();