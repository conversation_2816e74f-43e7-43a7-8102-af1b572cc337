// ==UserScript==
// @name         Nodeseek屏蔽
// @namespace    http://tampermonkey.net/
// @version      2.4
// @description  Adds keyword and username blocking functionality to Nodeseek. Hides posts based on keywords in title or by username (fuzzy match only).
// <AUTHOR>
// @match        *://www.nodeseek.com/*
// @grant        GM_getValue
// @grant        GM_setValue
// @grant        GM_addStyle
// @run-at      document-start
// @grant        GM_log
// ==/UserScript==

(function () {
    "use strict";

    // 调试日志函数，极简版本，几乎完全禁用日志输出以避免触发风控
    function log(message, ...args) {
        // 默认情况下完全禁用日志
        // 只有在明确启用调试模式且页面可见时才输出日志
        if (document.hidden || !window._nodeseekDebugMode) {
            return;
        }

        try {
            // 只记录错误和关键操作
            const isImportantLog = message.includes('错误') ||
                                  message.includes('初始化完成') ||
                                  message.includes('屏蔽用户');

            if (!isImportantLog) {
                return; // 跳过所有非重要日志
            }

            // 使用本地存储而不是控制台记录日志
            // 这样可以在需要时查看，但不会触发控制台活动
            try {
                const logs = JSON.parse(localStorage.getItem('nodeseek_debug_logs') || '[]');
                const timestamp = new Date().toISOString();
                logs.push(`[${timestamp}] ${message}`);

                // 只保留最近的50条日志
                if (logs.length > 50) {
                    logs.shift();
                }

                localStorage.setItem('nodeseek_debug_logs', JSON.stringify(logs));
            } catch (storageError) {
                // 如果本地存储失败，只在真正需要时使用控制台
                if (message.includes('错误')) {
                    console.error(`[Nodeseek Blocker] ${message}`, ...args);
                }
            }
        } catch (e) {
            // 出现异常时，不做任何处理，完全禁用日志
        }
    }

    // 默认禁用调试模式
    window._nodeseekDebugMode = false;

    // 错误处理函数，优化版本，减少控制台输出
    function handleError(error, context) {
        // 记录错误到本地存储而不是控制台
        try {
            const errors = JSON.parse(localStorage.getItem('nodeseek_errors') || '[]');
            const timestamp = new Date().toISOString();
            errors.push({
                timestamp,
                context,
                message: error.message,
                stack: error.stack
            });

            // 只保留最近的20条错误记录
            if (errors.length > 20) {
                errors.shift();
            }

            localStorage.setItem('nodeseek_errors', JSON.stringify(errors));

            // 只在调试模式下输出到控制台
            if (window._nodeseekDebugMode) {
                console.error(`[Nodeseek Blocker] 错误 (${context}):`, error);
            }
        } catch (e) {
            // 如果本地存储失败，只在真正严重的错误时使用控制台
            if (context.includes('初始化') || context.includes('主脚本')) {
                console.error(`[Nodeseek Blocker] 错误 (${context}):`, error);
            }
        }
    }

    try {
        log("脚本开始加载");

        // CSS 样式
        const customStyles = `
.block-list-button {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 1000;
    padding: 5px 10px;
    background: #000;
    box-shadow: 0 0 6px rgba(0, 0, 0, 0.2);
    color: #fff;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.3s ease;
}

.trigger-area {
    position: fixed;
    bottom: 0;
    right: 0;
    width: 100px;
    height: 100px;
    z-index: 999;
}

.trigger-area:hover .block-list-button {
    opacity: 1;
    pointer-events: auto;
}

.block-dialog {
	position: fixed;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	width: 610px;
	max-width: 90vw;
	max-height: 85vh;
	background: rgba(255, 255, 255, 0.5);
	padding: 20px;
	border-radius: 10px;
	z-index: 1001;
	display: none;
	transition: opacity 0.3s ease;
	box-shadow: 0 1px 60px rgba(0, 0, 0, 0.30), inset 0px 0px 20px 5px rgb(255, 255, 255);
}

.block-dialog::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 10px;
    z-index: -1;
    backdrop-filter: blur(25px);
}

.block-title-bar {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 30px;
    background: none;
    cursor: move;
    user-select: none;
}

.block-close-button {
    position: absolute;
    top: 12px;
    right: 12px;
    width: 16px;
    height: 16px;
    background: black;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    transition: background 0.15s ease;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
    overflow: hidden;
    text-indent: -9999px;
    padding: 0;
    z-index: 1002;
}

.block-close-button:hover {
    background: red;
}

.block-close-line {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 8px;
    height: 2px;
    background: white;
    transition: background 0.15s ease;
}

.block-close-line-1 {
    transform: translate(-50%, -50%) rotate(45deg);
}

.block-close-line-2 {
    transform: translate(-50%, -50%) rotate(-45deg);
}

.block-list-section {
    margin-bottom: 20px;
}

.block-list {
	margin: 10px;
	font-size: 12px;
	color: #333;
}

.block-input-container {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 15px;
    margin: 10px 0;
}

.block-input {
    border: 1px solid #dddddd;
    background: #ffffff96;
    border-radius: 8px;
    font-size: 13px;
    padding: 5px 10px;
    width: 180px;
}

.block-input:focus {
    border: 1px solid #999;
    box-shadow: 0 1px 10px rgba(0, 0, 0, 0.1)
}

.block-button {
    border-radius: 8px;
    border: 0px solid black;
    background: #000;
    padding: 5px 10px;
    color: white;
    font-size: 13px;
    cursor: pointer;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
}
h3.block-dialog-title {
	margin: 0 0 10px;
	font-size: 15px;
	color: #000;
    cursor: pointer;
    position: relative;
    padding: 5px 0 5px 20px;
    user-select: none;
    border-radius: 4px;
    transition: background-color 0.2s ease;
}

h3.block-dialog-title::before {
    content: "▼";
    position: absolute;
    left: 0;
    transition: transform 0.4s cubic-bezier(0.25, 1, 0.5, 1);
    font-size: 12px;
    display: inline-block;
}

h3.block-dialog-title.collapsed::before {
    transform: rotate(-90deg);
}

.block-list-content {
    max-height: 300px;
    transition: max-height 0.4s cubic-bezier(0.25, 1, 0.5, 1),
                opacity 0.4s cubic-bezier(0.25, 1, 0.5, 1),
                margin 0.4s cubic-bezier(0.25, 1, 0.5, 1),
                padding 0.4s cubic-bezier(0.25, 1, 0.5, 1);
    padding: 10px;
    background: rgba(0, 0, 0, 0.05);
    border-radius: 8px;
    margin-top: 5px;
    opacity: 1;
    will-change: max-height, opacity, margin, padding; /* 提示浏览器这些属性将会变化，优化性能 */
}

.block-list-content.collapsed {
    max-height: 0 !important; /* 使用!important确保覆盖内联样式 */
    overflow: hidden;
    opacity: 0;
    margin-top: 0;
    padding-top: 0;
    padding-bottom: 0;
}

/* Styles for the hover block button container and button itself */
.author-name-container,
.info-item.info-author,
.post-title-container {
    position: relative; /* Required for absolute positioning of the button */
    display: inline-block; /* Or block, depending on layout. Inline-block is usually good for text-like elements. */
    padding-right: 5px; /* Default small padding, expanded on hover */
    transition: padding-right 0.2s ease; /* Smooth transition for padding change */
}

.author-name-container:hover,
.info-item.info-author:hover,
.post-title-container:hover {
    padding-right: 40px; /* 悬停时扩展右侧填充，为按钮腾出空间 */
}

.block-user-hover-button,
.block-post-hover-button {
    display: none; /* 默认隐藏 */
    position: absolute;
    right: 0; /* 位于容器右侧 */
    top: 50%;
    transform: translateY(-50%); /* 垂直居中 */
    padding: 0px 7px;
    font-size: 10px;
    background-color: rgba(0, 0, 0); /* 黑色背景 */
    color: #fff; /* 白色文字 */
    border-radius: 6px;
    cursor: pointer;
    z-index: 100; /* 确保在其他元素之上 */
    box-shadow: 0px 1px 2px rgba(0,0,0,0.1);
    white-space: nowrap; /* 防止按钮文字换行 */
    opacity: 0; /* 初始透明 */
    transition: opacity 0.2s ease; /* 添加淡入淡出效果 */
}

/* 主页列表中的用户名容器样式已合并到上面的通用选择器中 */

/* 当鼠标悬停在用户名或帖子标题容器上时显示按钮 */
.author-name-container:hover .block-user-hover-button,
.info-item.info-author:hover .block-user-hover-button,
.post-title-container:hover .block-post-hover-button {
    display: inline-block; /* 在容器悬停时显示 */
    opacity: 1; /* 完全不透明 */
}

/* 屏蔽帖子列表样式 */
.post-item {
    display: flex;
    align-items: center;
    position: relative;
    margin: 5px 0;
    padding: 2px 8px;
    background: rgba(0, 0, 0, 0.05);
    border-radius: 6px;
    font-size: 12px;
    color: #444;
    border: 1px solid rgba(0, 0, 0, 0.08);
    width: 100%; /* 使用全部可用宽度 */
    box-sizing: border-box; /* 确保padding不会增加元素宽度 */
    padding-right: 25px; /* 为删除按钮留出足够空间 */
}

.post-title-text {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: calc(100% - 25px); /* 留出删除按钮的空间 */
    flex: 1; /* 让标题文本占据所有可用空间 */
    display: inline-block; /* 确保文本可以正确显示省略号 */
}

.keyword-item, .username-item {
	display: inline-block;
	background: rgba(0, 0, 0, 0.05);
	padding: 2px 8px;
	border-radius: 6px;
	margin: 5px 2px;
	font-size: 12px;
	position: relative;
	color: #444;
	border: 1px solid rgba(0, 0, 0, 0.08);
}

.keyword-item {
	display: inline-block;
}

.item-delete-button {
    display: none;
    position: absolute;
    top: -8px;
    right: -8px;
    width: 16px;
    height: 16px;
    background-color: #ff4d4f;
    color: white;
    border-radius: 50%;
    text-align: center;
    line-height: 14px;
    font-size: 12px;
    cursor: pointer;
    box-shadow: 0 1px 3px rgba(0,0,0,0.2);
    z-index: 10;
}

/* 帖子项中的删除按钮位置调整 */
.post-item .item-delete-button {
    top: 50%;
    right: 5px;
    transform: translateY(-50%);
    z-index: 20; /* 确保按钮在最上层 */
}

.keyword-item:hover .item-delete-button,
.username-item:hover .item-delete-button,
.post-item:hover .item-delete-button {
    display: block;
}

`;

        // 样式注入函数
        function injectStyles() {
            try {
                GM_addStyle(customStyles);
                console.log("Nodeseek Blocker: Styles applied via GM_addStyle.");
            } catch (error) {
                console.error("Nodeseek Blocker: GM_addStyle failed, falling back to <style> tag.", error);
                try {
                    const styleEl = document.createElement("style");
                    styleEl.id = "nodeseek-blocker-styles";
                    styleEl.textContent = customStyles;

                    if (document.head) {
                        document.head.appendChild(styleEl);
                    } else {
                        const headObserver = new MutationObserver(() => {
                            if (document.head && !document.getElementById("nodeseek-blocker-styles")) {
                                document.head.appendChild(styleEl);
                                headObserver.disconnect();
                            }
                        });
                        headObserver.observe(document.documentElement, { childList: true });
                        document.documentElement.appendChild(styleEl); // Temporary
                    }
                    console.log("Nodeseek Blocker: Styles applied via <style> tag.");
                } catch (fallbackError) {
                    console.error("Nodeseek Blocker: Style application failed completely.", fallbackError);
                }
            }
        }

        // 立即注入样式
        injectStyles();

        // 显示通知函数
        function showNotification(message) {
            if (!document.body) {
                setTimeout(() => showNotification(message), 100);
                return;
            }
            const notification = document.createElement("div");
            Object.assign(notification.style, {
                position: "fixed",
                bottom: "20px",
                left: "20px",
                backgroundColor: "rgba(0, 0, 0, 0.8)",
                color: "#fff",
                padding: "10px 20px",
                borderRadius: "8px",
                zIndex: "9999",
                transition: "opacity 0.3s ease",
                opacity: "0",
            });
            notification.textContent = message;
            document.body.appendChild(notification);
            setTimeout(() => { notification.style.opacity = "1"; }, 10);
            setTimeout(() => {
                notification.style.opacity = "0";
                setTimeout(() => {
                    if (document.body.contains(notification)) {
                        document.body.removeChild(notification);
                    }
                }, 300);
            }, 3000);
        }

        // 安全的存储操作函数
        function safeGetValue(key, defaultValue) {
            try {
                return GM_getValue(key, defaultValue) || defaultValue;
            } catch (error) {
                console.error(`Error getting value for ${key}:`, error);
                return defaultValue;
            }
        }

        function safeSetValue(key, value) {
            try {
                GM_setValue(key, value);
                return true;
            } catch (error) {
                console.error(`Error setting value for ${key}:`, error);
                showNotification(`保存失败: ${error.message}`);
                return false;
            }
        }

        // 获取存储的屏蔽列表和设置，使用Set数据结构提高查找效率
        let blockedKeywordsArray = safeGetValue("nodeseek_blockedKeywords", []);
        let blockedKeywords = new Set(blockedKeywordsArray);

        // 获取用户名列表，但限制只保留最近的50个
        let blockedUsernamesArray = safeGetValue("nodeseek_blockedUsernames", []).slice(0, 50);
        let blockedUsernames = new Set(blockedUsernamesArray);

        // 获取屏蔽帖子列表，但限制只保留最近的10个
        let blockedPosts = safeGetValue("nodeseek_blockedPosts", []).slice(0, 10);

        // 创建帖子ID到帖子对象的映射，提高查找效率
        let blockedPostsMap = new Map();
        blockedPosts.forEach(post => blockedPostsMap.set(post.id, post));

        // 防抖函数
        function debounce(func, wait) {
            let timeout;
            return function (...args) {
                clearTimeout(timeout);
                timeout = setTimeout(() => func.apply(this, args), wait);
            };
        }

        // 创建触发区域和按钮
        function createBlockButton() {
            if (document.querySelector(".trigger-area")) return;

            const triggerArea = document.createElement("div");
            triggerArea.className = "trigger-area";

            const button = document.createElement("button");
            button.innerText = "屏蔽列表";
            button.className = "block-list-button";
            button.addEventListener("click", toggleBlockListDialog);

            triggerArea.appendChild(button);
            document.body.appendChild(triggerArea);
        }

        let dialogElement = null; // To keep reference to the dialog

        // 切换屏蔽列表对话框显示/隐藏
        function toggleBlockListDialog() {
            if (!dialogElement) {
                createBlockListDialog();
            }
            if (dialogElement) {
                const isVisible = dialogElement.style.display === "block";

                if (!isVisible) {
                    // 先设置为可见但透明，以便计算高度
                    dialogElement.style.opacity = "0";
                    dialogElement.style.display = "block";

                    // 刷新列表内容
                    updateUsernameListDisplay();
                    updateKeywordListDisplay();

                    // 使用 requestAnimationFrame 确保在下一帧渲染前设置透明度
                    requestAnimationFrame(() => {
                        // 平滑显示对话框
                        dialogElement.style.opacity = "1";
                    });
                } else {
                    // 隐藏对话框
                    dialogElement.style.opacity = "0";
                    setTimeout(() => {
                        dialogElement.style.display = "none";
                    }, 300); // 与过渡时间匹配
                }
            }
        }

        // 删除关键词 - 优化版本，使用Set
        function removeKeyword(keyword) {
            if (blockedKeywords.has(keyword)) {
                blockedKeywords.delete(keyword);
                // 转换回数组以便存储
                const keywordsArray = Array.from(blockedKeywords);
                if (safeSetValue("nodeseek_blockedKeywords", keywordsArray)) {
                    updateKeywordListDisplay();
                    debouncedFilterContent();
                    showNotification(`已删除屏蔽关键词: ${keyword}`);
                }
            }
        }

        function updateKeywordListDisplay() {
            if (dialogElement) {
                const keywordListDiv = dialogElement.querySelector(".keyword-block-list");
                if (keywordListDiv) {
                    // 获取当前折叠状态，优先从localStorage读取
                    let isCollapsed = false;
                    try {
                        const savedState = localStorage.getItem("nodeseek_keywords_collapsed");
                        if (savedState !== null) {
                            isCollapsed = savedState === "true";
                        } else {
                            // 如果没有保存的状态，则使用当前DOM状态
                            isCollapsed = keywordListDiv.querySelector(".block-dialog-title")?.classList.contains("collapsed") || false;
                        }
                    } catch (e) {
                        // 如果localStorage访问失败，则使用当前DOM状态
                        isCollapsed = keywordListDiv.querySelector(".block-dialog-title")?.classList.contains("collapsed") || false;
                    }

                    if (blockedKeywords.size > 0) {
                        let keywordsHtml = `<h3 class="block-dialog-title${isCollapsed ? ' collapsed' : ''}">屏蔽的关键词:</h3>`;
                        keywordsHtml += `<div class="block-list-content${isCollapsed ? ' collapsed' : ''}">`;

                        // 将Set转换为数组以便遍历
                        Array.from(blockedKeywords).forEach(kw => {
                            keywordsHtml += `
                                <div class="keyword-item">
                                    <span>${kw}</span>
                                    <span class="item-delete-button" data-keyword="${kw}">×</span>
                                </div>
                            `;
                        });

                        keywordsHtml += `</div>`;
                        keywordListDiv.innerHTML = keywordsHtml;

                        // 添加删除关键词的事件监听
                        keywordListDiv.querySelectorAll('.item-delete-button').forEach(deleteBtn => {
                            deleteBtn.addEventListener('click', function(e) {
                                e.stopPropagation(); // 防止事件冒泡
                                const keywordToDelete = this.getAttribute('data-keyword');
                                removeKeyword(keywordToDelete);
                            });
                        });
                    } else {
                        let keywordsHtml = `<h3 class="block-dialog-title${isCollapsed ? ' collapsed' : ''}">屏蔽的关键词:</h3>`;
                        keywordsHtml += `<div class="block-list-content${isCollapsed ? ' collapsed' : ''}">`;
                        keywordsHtml += `<div style='font-style:italic;color:#999;text-align:center;margin-top:10px;'>暂无屏蔽关键词</div>`;
                        keywordsHtml += `</div>`;
                        keywordListDiv.innerHTML = keywordsHtml;
                    }

                    // 添加点击事件
                    const title = keywordListDiv.querySelector(".block-dialog-title");
                    if (title) {
                        title.addEventListener("click", function(e) {
                            e.preventDefault(); // 防止事件冒泡
                            this.classList.toggle("collapsed");
                            const content = this.nextElementSibling;
                            if (content && content.classList.contains("block-list-content")) {
                                // 平滑过渡：如果是展开，先设置maxHeight为0，然后在下一帧设置为scrollHeight
                                if (this.classList.contains("collapsed")) {
                                    // 折叠
                                    content.classList.add("collapsed");
                                } else {
                                    // 展开
                                    // 先获取内容的实际高度
                                    content.style.maxHeight = "none";
                                    const actualHeight = content.scrollHeight;
                                    // 重置为折叠状态
                                    content.style.maxHeight = "0px";
                                    content.classList.remove("collapsed");

                                    // 在下一帧设置为实际高度
                                    requestAnimationFrame(() => {
                                        content.style.maxHeight = actualHeight + "px";
                                        // 动画完成后重置为默认值
                                        setTimeout(() => {
                                            content.style.maxHeight = "";
                                        }, 400); // 与过渡时间匹配
                                    });
                                }

                                // 保存折叠状态到localStorage
                                try {
                                    localStorage.setItem("nodeseek_keywords_collapsed", this.classList.contains("collapsed"));
                                } catch (e) {
                                    // 忽略localStorage错误
                                }
                            }
                        });
                    }
                }
            }
        }

        // 删除用户名 - 优化版本，使用Set
        function removeUsername(username) {
            if (blockedUsernames.has(username)) {
                blockedUsernames.delete(username);
                // 转换回数组以便存储
                const usernamesArray = Array.from(blockedUsernames);
                if (safeSetValue("nodeseek_blockedUsernames", usernamesArray)) {
                    updateUsernameListDisplay();
                    debouncedFilterContent();
                    showNotification(`已删除屏蔽用户名: ${username}`);
                }
            }
        }

        // 添加屏蔽帖子 - 优化版本，使用Map
        function addBlockedPost(postId, postTitle) {
            // 创建新的帖子对象
            const newPost = {
                id: postId,
                title: postTitle,
                time: Date.now() // 添加时间戳，方便排序
            };

            // 更新Map
            blockedPostsMap.set(postId, newPost);

            // 重建数组，确保新帖子在最前面
            blockedPosts = [newPost];

            // 添加其他帖子，但跳过刚添加的帖子
            blockedPostsMap.forEach((post, id) => {
                if (id !== postId) {
                    blockedPosts.push(post);
                }
            });

            // 确保列表长度不超过10个
            if (blockedPosts.length > 10) {
                blockedPosts = blockedPosts.slice(0, 10);
                // 同步更新Map
                blockedPostsMap.clear();
                blockedPosts.forEach(post => blockedPostsMap.set(post.id, post));
            }

            if (safeSetValue("nodeseek_blockedPosts", blockedPosts)) {
                updatePostListDisplay();
                debouncedFilterContent();
                showNotification(`已屏蔽帖子: ${postTitle}`);
                return true;
            }
            return false;
        }

        // 删除屏蔽帖子 - 优化版本，使用Map
        function removeBlockedPost(postId) {
            if (blockedPostsMap.has(postId)) {
                const postTitle = blockedPostsMap.get(postId).title;

                // 从Map中删除
                blockedPostsMap.delete(postId);

                // 更新数组
                blockedPosts = blockedPosts.filter(post => post.id !== postId);

                if (safeSetValue("nodeseek_blockedPosts", blockedPosts)) {
                    updatePostListDisplay();
                    debouncedFilterContent();
                    showNotification(`已删除屏蔽帖子: ${postTitle}`);
                }
            }
        }

        // 更新屏蔽帖子列表显示
        function updatePostListDisplay() {
            if (dialogElement) {
                const postListDiv = dialogElement.querySelector(".post-block-list");
                if (postListDiv) {
                    // 获取当前折叠状态，优先从localStorage读取
                    let isCollapsed = false;
                    try {
                        const savedState = localStorage.getItem("nodeseek_posts_collapsed");
                        if (savedState !== null) {
                            isCollapsed = savedState === "true";
                        } else {
                            // 如果没有保存的状态，则使用当前DOM状态
                            isCollapsed = postListDiv.querySelector(".block-dialog-title")?.classList.contains("collapsed") || false;
                        }
                    } catch (e) {
                        // 如果localStorage访问失败，则使用当前DOM状态
                        isCollapsed = postListDiv.querySelector(".block-dialog-title")?.classList.contains("collapsed") || false;
                    }

                    if (blockedPosts.length > 0) {
                        let postsHtml = `<h3 class="block-dialog-title${isCollapsed ? ' collapsed' : ''}">屏蔽的帖子 (显示最近10个):</h3>`;
                        postsHtml += `<div class="block-list-content${isCollapsed ? ' collapsed' : ''}">`;

                        blockedPosts.forEach(post => {
                            // 截断标题，如果太长则用...代替，但允许更长的标题
                            // 在UI中，我们依赖CSS的text-overflow: ellipsis来处理截断
                            // 这里只是为了title属性提供一个合理的长度
                            const truncatedTitle = post.title;

                            postsHtml += `
                                <div class="post-item">
                                    <span class="post-title-text" title="${post.title}">${truncatedTitle}</span>
                                    <span class="item-delete-button" data-post-id="${post.id}" title="删除此屏蔽">×</span>
                                </div>
                            `;
                        });

                        postsHtml += `</div>`;
                        postListDiv.innerHTML = postsHtml;

                        // 添加删除帖子的事件监听
                        postListDiv.querySelectorAll('.item-delete-button').forEach(deleteBtn => {
                            deleteBtn.addEventListener('click', function(e) {
                                e.stopPropagation(); // 防止事件冒泡
                                const postIdToDelete = this.getAttribute('data-post-id');
                                removeBlockedPost(postIdToDelete);
                            });
                        });
                    } else {
                        let postsHtml = `<h3 class="block-dialog-title${isCollapsed ? ' collapsed' : ''}">屏蔽的帖子:</h3>`;
                        postsHtml += `<div class="block-list-content${isCollapsed ? ' collapsed' : ''}">`;
                        postsHtml += `<div style='font-style:italic;color:#999;text-align:center;margin-top:10px;'>暂无屏蔽帖子</div>`;
                        postsHtml += `</div>`;
                        postListDiv.innerHTML = postsHtml;
                    }

                    // 添加点击事件
                    const title = postListDiv.querySelector(".block-dialog-title");
                    if (title) {
                        title.addEventListener("click", function(e) {
                            e.preventDefault(); // 防止事件冒泡
                            this.classList.toggle("collapsed");
                            const content = this.nextElementSibling;
                            if (content && content.classList.contains("block-list-content")) {
                                // 平滑过渡：如果是展开，先设置maxHeight为0，然后在下一帧设置为scrollHeight
                                if (this.classList.contains("collapsed")) {
                                    // 折叠
                                    content.classList.add("collapsed");
                                } else {
                                    // 展开
                                    // 先获取内容的实际高度
                                    content.style.maxHeight = "none";
                                    const actualHeight = content.scrollHeight;
                                    // 重置为折叠状态
                                    content.style.maxHeight = "0px";
                                    content.classList.remove("collapsed");

                                    // 在下一帧设置为实际高度
                                    requestAnimationFrame(() => {
                                        content.style.maxHeight = actualHeight + "px";
                                        // 动画完成后重置为默认值
                                        setTimeout(() => {
                                            content.style.maxHeight = "";
                                        }, 400); // 与过渡时间匹配
                                    });
                                }

                                // 保存折叠状态到localStorage
                                try {
                                    localStorage.setItem("nodeseek_posts_collapsed", this.classList.contains("collapsed"));
                                } catch (e) {
                                    // 忽略localStorage错误
                                }
                            }
                        });
                    }
                }
            }
        }

        function updateUsernameListDisplay() {
            if (dialogElement) {
                const usernameListDiv = dialogElement.querySelector(".username-block-list");
                if (usernameListDiv) {
                    // 获取当前折叠状态，优先从localStorage读取
                    let isCollapsed = false;
                    try {
                        const savedState = localStorage.getItem("nodeseek_usernames_collapsed");
                        if (savedState !== null) {
                            isCollapsed = savedState === "true";
                        } else {
                            // 如果没有保存的状态，则使用当前DOM状态
                            isCollapsed = usernameListDiv.querySelector(".block-dialog-title")?.classList.contains("collapsed") || false;
                        }
                    } catch (e) {
                        // 如果localStorage访问失败，则使用当前DOM状态
                        isCollapsed = usernameListDiv.querySelector(".block-dialog-title")?.classList.contains("collapsed") || false;
                    }

                    if (blockedUsernames.size > 0) {
                        // 将Set转换为数组并只显示最近的50个用户名
                        const displayUsernames = Array.from(blockedUsernames).slice(0, 50);
                        let usernamesHtml = `<h3 class="block-dialog-title${isCollapsed ? ' collapsed' : ''}">屏蔽的用户名 (显示最近50个):</h3>`;
                        usernamesHtml += `<div class="block-list-content${isCollapsed ? ' collapsed' : ''}">`;

                        displayUsernames.forEach(username => {
                            usernamesHtml += `
                                <div class="username-item">
                                    <span>${username}</span>
                                    <span class="item-delete-button" data-username="${username}">×</span>
                                </div>
                            `;
                        });

                        usernamesHtml += `</div>`;
                        usernameListDiv.innerHTML = usernamesHtml;

                        // 添加删除用户名的事件监听
                        usernameListDiv.querySelectorAll('.item-delete-button').forEach(deleteBtn => {
                            deleteBtn.addEventListener('click', function(e) {
                                e.stopPropagation(); // 防止事件冒泡
                                const usernameToDelete = this.getAttribute('data-username');
                                removeUsername(usernameToDelete);
                            });
                        });
                    } else {
                        let usernamesHtml = `<h3 class="block-dialog-title${isCollapsed ? ' collapsed' : ''}">屏蔽的用户名:</h3>`;
                        usernamesHtml += `<div class="block-list-content${isCollapsed ? ' collapsed' : ''}">`;
                        usernamesHtml += `<div style='font-style:italic;color:#999;text-align:center;margin-top:10px;'>暂无屏蔽用户名</div>`;
                        usernamesHtml += `</div>`;
                        usernameListDiv.innerHTML = usernamesHtml;
                    }

                    // 添加点击事件
                    const title = usernameListDiv.querySelector(".block-dialog-title");
                    if (title) {
                        title.addEventListener("click", function(e) {
                            e.preventDefault(); // 防止事件冒泡
                            this.classList.toggle("collapsed");
                            const content = this.nextElementSibling;
                            if (content && content.classList.contains("block-list-content")) {
                                // 平滑过渡：如果是展开，先设置maxHeight为0，然后在下一帧设置为scrollHeight
                                if (this.classList.contains("collapsed")) {
                                    // 折叠
                                    content.classList.add("collapsed");
                                } else {
                                    // 展开
                                    // 先获取内容的实际高度
                                    content.style.maxHeight = "none";
                                    const actualHeight = content.scrollHeight;
                                    // 重置为折叠状态
                                    content.style.maxHeight = "0px";
                                    content.classList.remove("collapsed");

                                    // 在下一帧设置为实际高度
                                    requestAnimationFrame(() => {
                                        content.style.maxHeight = actualHeight + "px";
                                        // 动画完成后重置为默认值
                                        setTimeout(() => {
                                            content.style.maxHeight = "";
                                        }, 400); // 与过渡时间匹配
                                    });
                                }

                                // 保存折叠状态到localStorage
                                try {
                                    localStorage.setItem("nodeseek_usernames_collapsed", this.classList.contains("collapsed"));
                                } catch (e) {
                                    // 忽略localStorage错误
                                }
                            }
                        });
                    }
                }
            }
        }

        // 创建屏蔽列表对话框
        function createBlockListDialog() {
            if (document.querySelector(".block-dialog")) return;

            dialogElement = document.createElement("div");
            dialogElement.className = "block-dialog";

            const titleBar = document.createElement("div");
            titleBar.className = "block-title-bar";

            let isDragging = false;
            let startX, startY, initialLeft, initialTop;

            titleBar.addEventListener("mousedown", (e) => {
                if (e.target === titleBar) {
                    isDragging = true;
                    startX = e.clientX;
                    startY = e.clientY;
                    const rect = dialogElement.getBoundingClientRect();
                    initialLeft = rect.left;
                    initialTop = rect.top;
                    dialogElement.style.transform = "none"; // Remove transform to use left/top
                    dialogElement.style.left = `${initialLeft}px`;
                    dialogElement.style.top = `${initialTop}px`;
                    document.addEventListener("mousemove", doDrag);
                    document.addEventListener("mouseup", stopDragging);
                }
            });

            function doDrag(e) {
                if (!isDragging) return;
                e.preventDefault();
                dialogElement.style.left = `${initialLeft + (e.clientX - startX)}px`;
                dialogElement.style.top = `${initialTop + (e.clientY - startY)}px`;
            }

            function stopDragging() {
                if (isDragging) {
                    isDragging = false;
                    document.removeEventListener("mousemove", doDrag);
                    document.removeEventListener("mouseup", stopDragging);
                }
            }

            const closeButton = document.createElement("button");
            closeButton.className = "block-close-button";
            closeButton.innerHTML = `<span class="block-close-line block-close-line-1"></span><span class="block-close-line block-close-line-2"></span>`;
            closeButton.onclick = () => {
                dialogElement.style.display = "none";
                stopDragging(); // Ensure mouse listeners are removed
            };

            // 关键词屏蔽部分
            const keywordSection = document.createElement("div");
            keywordSection.className = "block-list-section";

            const keywordList = document.createElement("div");
            keywordList.className = "block-list keyword-block-list";
            // Content will be set by updateKeywordListDisplay

            const keywordInputContainer = document.createElement("div");
            keywordInputContainer.className = "block-input-container";

            const keywordInput = document.createElement("input");
            keywordInput.type = "text";
            keywordInput.placeholder = "输入关键词";
            keywordInput.className = "block-input";

            const addKeywordButton = document.createElement("button");
            addKeywordButton.innerText = "添加关键词";
            addKeywordButton.className = "block-button";

            addKeywordButton.onclick = () => {
                const newKeyword = keywordInput.value.trim().toLowerCase();
                if (newKeyword) {
                    // 使用Set的特性，直接添加（Set会自动处理重复项）
                    blockedKeywords.delete(newKeyword); // 先删除以确保新添加的在转换为数组时位于前面
                    blockedKeywords.add(newKeyword);

                    // 转换回数组以便存储，确保新添加的关键词在前面
                    const keywordsArray = [newKeyword, ...Array.from(blockedKeywords).filter(k => k !== newKeyword)];
                    blockedKeywords = new Set(keywordsArray); // 更新Set

                    if (safeSetValue("nodeseek_blockedKeywords", keywordsArray)) {
                        updateKeywordListDisplay();
                        keywordInput.value = "";
                        debouncedFilterContent();
                        showNotification(`已添加屏蔽关键词: ${newKeyword}`);
                    }
                }
            };

            keywordInput.addEventListener("keypress", (e) => {
                if (e.key === "Enter") {
                    addKeywordButton.click();
                }
            });

            keywordInputContainer.appendChild(keywordInput);
            keywordInputContainer.appendChild(addKeywordButton);
            keywordSection.appendChild(keywordList);
            keywordSection.appendChild(keywordInputContainer);

            // 用户名屏蔽部分 (新增)
            const usernameSection = document.createElement("div");
            usernameSection.className = "block-list-section";

            const usernameList = document.createElement("div");
            usernameList.className = "block-list username-block-list";

            const usernameInputContainer = document.createElement("div");
            usernameInputContainer.className = "block-input-container";

            const usernameInput = document.createElement("input");
            usernameInput.type = "text";
            usernameInput.placeholder = "输入用户名";
            usernameInput.className = "block-input";

            const addUsernameButton = document.createElement("button");
            addUsernameButton.innerText = "添加用户";
            addUsernameButton.className = "block-button";

            addUsernameButton.onclick = () => {
                const newUsername = usernameInput.value.trim();
                if (newUsername) {
                    // 使用Set的特性，直接添加（Set会自动处理重复项）
                    blockedUsernames.delete(newUsername); // 先删除以确保新添加的在转换为数组时位于前面
                    blockedUsernames.add(newUsername);

                    // 转换回数组以便存储，确保新添加的用户名在前面
                    const usernamesArray = [newUsername, ...Array.from(blockedUsernames).filter(u => u !== newUsername)];

                    // 确保列表长度不超过50个
                    const limitedArray = usernamesArray.slice(0, 50);
                    blockedUsernames = new Set(limitedArray); // 更新Set

                    if (safeSetValue("nodeseek_blockedUsernames", limitedArray)) {
                        updateUsernameListDisplay();
                        usernameInput.value = "";
                        debouncedFilterContent();
                        showNotification(`已添加屏蔽用户名: ${newUsername}`);
                    }
                }
            };

            usernameInput.addEventListener("keypress", (e) => {
                if (e.key === "Enter") {
                    addUsernameButton.click();
                }
            });

            usernameInputContainer.appendChild(usernameInput);
            usernameInputContainer.appendChild(addUsernameButton);
            usernameSection.appendChild(usernameList);
            usernameSection.appendChild(usernameInputContainer);

            // 不再添加设置区域，因为自动隐藏被屏蔽的帖子已经是默认行为

            // 帖子屏蔽部分
            const postSection = document.createElement("div");
            postSection.className = "block-list-section";

            const postList = document.createElement("div");
            postList.className = "block-list post-block-list";
            // Content will be set by updatePostListDisplay

            postSection.appendChild(postList);

            dialogElement.appendChild(titleBar);
            dialogElement.appendChild(closeButton);
            dialogElement.appendChild(usernameSection);
            dialogElement.appendChild(keywordSection);
            dialogElement.appendChild(postSection);

            document.body.appendChild(dialogElement);
            updateUsernameListDisplay();
            updateKeywordListDisplay();
            updatePostListDisplay();
        }

        // 检查内容是否应该被屏蔽 - 优化版本，使用Set
        function shouldBlockContent(title) {
            if (!title || blockedKeywords.size === 0) return false;
            const lowerTitle = title.toLowerCase();

            // 将Set转换为数组进行检查
            // 这里使用Array.from而不是展开运算符，以避免大型Set的性能问题
            return Array.from(blockedKeywords).some(keyword => {
                let matchKeyword = keyword;
                let startsWithAnchor = false;
                let endsWithAnchor = false;

                if (matchKeyword.startsWith('^')) {
                    matchKeyword = matchKeyword.slice(1);
                    startsWithAnchor = true;
                }
                if (matchKeyword.endsWith('$')) {
                    matchKeyword = matchKeyword.slice(0, -1);
                    endsWithAnchor = true;
                }

                // 使用更高效的条件检查顺序
                if (startsWithAnchor && endsWithAnchor) {
                    return lowerTitle === matchKeyword;
                } else if (startsWithAnchor) {
                    return lowerTitle.startsWith(matchKeyword);
                } else if (endsWithAnchor) {
                    return lowerTitle.endsWith(matchKeyword);
                } else {
                    return lowerTitle.includes(matchKeyword);
                }
            });
        }

        // 过滤内容
        function filterContent() {
            try {
                log(`运行内容过滤，关键词: ${Array.from(blockedKeywords).join(', ')}, 用户名: ${Array.from(blockedUsernames).join(', ')}`);

                // 处理用户名屏蔽 - 简化版本，直接查找并屏蔽
                let totalBlockedByUsername = 0;

                // 如果有屏蔽的用户名，首先处理
                if (blockedUsernames.size > 0) {
                    // 在主页上，直接查找所有帖子项
                    if (window.location.pathname === '/' || window.location.pathname === '') {
                        // 获取所有帖子项
                        const allPostItems = document.querySelectorAll('li.post-list-item');
                        log(`在主页上找到 ${allPostItems.length} 个帖子项`);

                        // 遍历所有帖子项
                        allPostItems.forEach(postItem => {
                            // 查找帖子作者
                            const authorLinks = postItem.querySelectorAll('.info-item.info-author a, a[href^="/space/"]:not(.info-item.info-last-commenter a)');

                            // 检查每个作者链接
                            authorLinks.forEach(authorLink => {
                                const username = authorLink.textContent.trim();
                                if (blockedUsernames.has(username)) {
                                    log(`找到需要屏蔽的用户 "${username}" 的帖子`);

                                    // 直接屏蔽整个帖子项
                                    postItem.style.display = 'none';
                                    postItem.setAttribute('data-blocked', 'true');
                                    postItem.setAttribute('data-blocked-reason', 'username');
                                    postItem.setAttribute('data-blocked-value', username);
                                    totalBlockedByUsername++;
                                }
                            });
                        });
                    } else {
                        // 在其他页面上，使用更通用的方法
                        const usernameSelectors = [
                            '.author-name',
                            'div.post-meta a[href^="/user/"]',
                            '.meta-item a.author-link',
                            '.author > a',
                            '.comment-author a',
                            'a.username',
                            '.user-name',
                            'a[href^="/space/"]:not(.info-item.info-last-commenter a)'
                        ];

                        // 遍历所有用户名选择器
                        usernameSelectors.forEach(selector => {
                            const userElements = document.querySelectorAll(selector);

                            // 检查每个用户名元素
                            userElements.forEach(userElement => {
                                const username = userElement.textContent.trim();
                                if (blockedUsernames.has(username)) {
                                    log(`找到需要屏蔽的用户名: "${username}"`);

                                    // 首先检查是否在帖子内页
                                    const isPostDetailPage = userElement.closest('.nsk-content-meta-info');
                                    if (isPostDetailPage) {
                                        // 在帖子内页，需要隐藏整个帖子内容
                                        log(`在帖子内页找到需要屏蔽的用户 "${username}"`);

                                        // 首先尝试找到 content-item 元素（评论项）
                                        const contentItem = isPostDetailPage.closest('li.content-item');
                                        if (contentItem) {
                                            // 隐藏整个评论项
                                            log(`隐藏评论项 content-item`);
                                            contentItem.style.display = 'none';
                                            contentItem.setAttribute('data-blocked', 'true');
                                            contentItem.setAttribute('data-blocked-reason', 'username');
                                            contentItem.setAttribute('data-blocked-value', username);
                                            totalBlockedByUsername++;
                                            return; // 已处理评论项，不需要继续查找
                                        }
                                    }

                                    // 查找最外层的帖子容器
                                    let postContainer = userElement.closest('li.post-list-item');
                                    if (!postContainer) {
                                        // 如果找不到 li.post-list-item，尝试其他容器
                                        postContainer = userElement.closest('article.post') ||
                                                       userElement.closest('.comment-item') ||
                                                       userElement.closest('.post-item');
                                    }

                                    if (postContainer) {
                                        log(`屏蔽用户 "${username}" 的帖子/评论`);
                                        postContainer.style.display = 'none';
                                        postContainer.setAttribute('data-blocked', 'true');
                                        postContainer.setAttribute('data-blocked-reason', 'username');
                                        postContainer.setAttribute('data-blocked-value', username);
                                        totalBlockedByUsername++;
                                    }
                                }
                            });
                        });
                    }
                }

                // 处理关键词和帖子屏蔽
                let totalBlockedByKeyword = 0;
                let totalBlockedByPost = 0;

                // 获取所有帖子项
                const allPosts = document.querySelectorAll('li.post-list-item, article.post, .post-item');

                // 遍历所有帖子
                allPosts.forEach(post => {
                    // 如果已经被用户名屏蔽，跳过
                    if (post.getAttribute('data-blocked-reason') === 'username') {
                        return;
                    }

                    // 查找帖子标题
                    const titleElement = post.querySelector('div.post-title > a, .post-title a, a.post-title, .topic-title a, h2 a, h3 a, .title a');

                    if (titleElement) {
                        const titleText = titleElement.textContent.trim();

                        // 获取帖子ID
                        let postId = null;
                        const href = titleElement.getAttribute('href');
                        if (href) {
                            const postIdMatch = href.match(/\/post-(\d+)/) || href.match(/\/t\/(\d+)/) || href.match(/\/thread-(\d+)/);
                            if (postIdMatch && postIdMatch[1]) {
                                postId = postIdMatch[1];
                            } else {
                                postId = href;
                            }
                        }

                        // 检查是否被单独屏蔽 - 使用Map提高查找效率
                        const isPostBlocked = postId && blockedPostsMap.has(postId);

                        if (isPostBlocked) {
                            totalBlockedByPost++;
                            log(`屏蔽帖子 (单独屏蔽): ${titleText}`);

                            // 屏蔽帖子
                            post.style.display = 'none';
                            post.setAttribute('data-blocked', 'true');
                            post.setAttribute('data-blocked-reason', 'post');
                            post.setAttribute('data-blocked-value', postId);
                        } else if (shouldBlockContent(titleText)) {
                            totalBlockedByKeyword++;
                            log(`屏蔽帖子 (关键词): ${titleText}`);

                            // 屏蔽帖子
                            post.style.display = 'none';
                            post.setAttribute('data-blocked', 'true');
                            post.setAttribute('data-blocked-reason', 'keyword');
                            post.setAttribute('data-blocked-value', 'keyword');
                        } else {
                            // 如果之前被屏蔽过，现在应该显示
                            if (post.hasAttribute('data-blocked')) {
                                post.removeAttribute('data-blocked');
                                post.removeAttribute('data-blocked-reason');
                                post.removeAttribute('data-blocked-value');
                                post.style.display = '';
                            }
                        }
                    }
                });

                log(`总共屏蔽了 ${totalBlockedByUsername} 个用户的帖子，${totalBlockedByKeyword} 个关键词帖子，${totalBlockedByPost} 个单独屏蔽的帖子。`);
            } catch (error) {
                handleError(error, "过滤内容");
            }
        }

        const debouncedFilterContent = debounce(filterContent, 300);

        // 缓存常用DOM选择器结果
        const domCache = {
            postItems: null,
            authorLinks: null,
            titleElements: null,
            mainContentContainer: null,
            lastCacheTime: 0
        };

        // 刷新DOM缓存的函数
        function refreshDomCache() {
            // 如果距离上次缓存时间不足5秒，不刷新
            const now = Date.now();
            if (now - domCache.lastCacheTime < 5000) {
                return;
            }

            // 缓存常用的DOM元素
            domCache.postItems = document.querySelectorAll('li.post-list-item');
            domCache.authorLinks = document.querySelectorAll('.info-item.info-author a, a[href^="/space/"]:not(.info-item.info-last-commenter a)');
            domCache.titleElements = document.querySelectorAll('div.post-title > a, .post-title a, a.post-title, .topic-title a');

            // 尝试找到主要内容容器
            if (!domCache.mainContentContainer) {
                const mainContentSelectors = [
                    '.post-list',
                    '#topic-list',
                    '#content-area',
                    'main',
                    '.main-content',
                    '.content-area'
                ];

                for (const selector of mainContentSelectors) {
                    const element = document.querySelector(selector);
                    if (element) {
                        domCache.mainContentContainer = element;
                        break;
                    }
                }
            }

            domCache.lastCacheTime = now;
        }

        // 初始化 - 优化版本，使用DOM缓存
        function init() {
            try {
                if (!document.body) {
                    setTimeout(init, 100); // Wait for body
                    return;
                }
                log("初始化脚本");

                // 初始化DOM缓存
                refreshDomCache();

                // 创建UI元素
                try {
                    createBlockButton();
                    addBlockUserButtonsToUsernames();
                    addBlockPostButtonsToTitles();
                } catch (uiError) {
                    handleError(uiError, "创建UI");
                }

                // 分析页面结构并记录日志
                try {
                    analyzePageStructure();
                } catch (analyzeError) {
                    handleError(analyzeError, "分析页面结构");
                }

                // 等待页面完全加载后再过滤内容
                setTimeout(() => {
                    try {
                        // 检查是否在主页
                        if (window.location.pathname === '/' || window.location.pathname === '') {
                            log("检测到在主页，进行特殊处理");
                            // 在主页上，确保能正确识别和屏蔽用户帖子
                            const allPostItems = document.querySelectorAll('li.post-list-item');
                            log(`在主页上找到 ${allPostItems.length} 个帖子项`);

                            // 如果有屏蔽的用户名，检查每个帖子
                            if (blockedUsernames.size > 0) {
                                allPostItems.forEach(postItem => {
                                    // 只检查帖子作者，忽略最后评论者
                                    // 帖子作者通常在 .info-item.info-author 中，而最后评论者在 .info-item.info-last-commenter 中
                                    const authorLinks = postItem.querySelectorAll('.info-item.info-author a');

                                    // 如果找不到特定的作者链接，则使用更通用的选择器，但排除最后评论者
                                    if (authorLinks.length === 0) {
                                        const allUserLinks = postItem.querySelectorAll('a[href^="/space/"]');
                                        allUserLinks.forEach(userLink => {
                                            // 确保不是最后评论者
                                            if (!userLink.closest('.info-item.info-last-commenter')) {
                                                const username = userLink.textContent.trim();
                                                if (blockedUsernames.has(username)) {
                                                    log(`在主页上找到需要屏蔽的用户 "${username}" 的帖子`);
                                                    // 在主页上，确保只隐藏整个帖子项，而不是内部的内容
                                                    // 始终自动隐藏被屏蔽的帖子
                                                    postItem.style.display = 'none';
                                                    postItem.setAttribute('data-blocked', 'true');
                                                    postItem.setAttribute('data-blocked-reason', 'username');
                                                    postItem.setAttribute('data-blocked-value', username);
                                                }
                                            }
                                        });
                                    } else {
                                        // 使用特定的作者链接
                                        authorLinks.forEach(authorLink => {
                                            const username = authorLink.textContent.trim();
                                            if (blockedUsernames.has(username)) {
                                                log(`在主页上找到需要屏蔽的用户 "${username}" 的帖子`);
                                                // 在主页上，确保只隐藏整个帖子项，而不是内部的内容
                                                // 始终自动隐藏被屏蔽的帖子
                                                // 尝试找到最外层的 li.post-list-item
                                                let itemToActuallyBlock = postItem;

                                                // 先标记当前元素
                                                postItem.setAttribute('data-blocked', 'true');
                                                postItem.setAttribute('data-blocked-reason', 'username');
                                                postItem.setAttribute('data-blocked-value', username);

                                                // 如果当前元素不是 li.post-list-item，尝试找到它
                                                if (!postItem.classList.contains('post-list-item') && postItem.tagName.toLowerCase() !== 'li') {
                                                    const listItem = postItem.closest('li.post-list-item');
                                                    if (listItem) {
                                                        itemToActuallyBlock = listItem;
                                                    }
                                                }

                                                // 隐藏找到的最合适元素
                                                itemToActuallyBlock.style.display = 'none';
                                                itemToActuallyBlock.setAttribute('data-blocked', 'true');
                                                itemToActuallyBlock.setAttribute('data-blocked-reason', 'username');
                                                itemToActuallyBlock.setAttribute('data-blocked-value', username);
                                            }
                                        });
                                    }
                                });
                            }
                        }

                        filterContent(); // Initial filter pass
                        log("初始过滤完成");

                        // 再次分析页面结构，确保没有遗漏
                        analyzePageStructure();
                    } catch (filterError) {
                        handleError(filterError, "初始过滤");
                    }
                }, 150);

                // 设置观察器
                try {
                    setupMutationObserver();
                } catch (observerError) {
                    handleError(observerError, "设置观察器");
                }
            } catch (error) {
                handleError(error, "初始化");
            }
        }

        // 设置 MutationObserver，极度优化性能版本
        function setupMutationObserver() {
            // 如果页面不可见，不启动观察器
            if (document.hidden) {
                return;
            }

            // 如果已有观察器，先断开连接
            if (window._nodeseekBlockerObserver) {
                window._nodeseekBlockerObserver.disconnect();
            }

            // 记录上次处理时间，避免频繁处理
            let lastProcessTime = Date.now();

            // 创建一个超级节流版本的处理函数
            let throttleTimer = null;
            let pendingChanges = false;

            const throttledFilter = () => {
                if (throttleTimer) {
                    // 如果已经有定时器在等待，只标记有待处理的变化
                    pendingChanges = true;
                    return;
                }

                const now = Date.now();
                const timeSinceLastProcess = now - lastProcessTime;

                // 如果距离上次处理不足3秒，延迟处理
                if (timeSinceLastProcess < 3000) {
                    throttleTimer = setTimeout(() => {
                        if (pendingChanges && !document.hidden) {
                            debouncedFilterContent();
                            // 始终添加屏蔽按钮
                            addBlockUserButtonsToUsernames();
                            addBlockPostButtonsToTitles();
                            lastProcessTime = Date.now();
                        }
                        throttleTimer = null;
                        pendingChanges = false;
                    }, 3000 - timeSinceLastProcess);
                    return;
                }

                // 如果距离上次处理已经足够长，直接处理
                if (!document.hidden) {
                    debouncedFilterContent();
                    // 始终添加屏蔽按钮
                    addBlockUserButtonsToUsernames();
                    addBlockPostButtonsToTitles();
                    lastProcessTime = now;
                }
                pendingChanges = false;
            };

            // 使用更高效的观察器配置和处理逻辑
            const observer = new MutationObserver((mutationsList) => {
                try {
                    // 如果页面不可见或变化太少，不处理
                    if (document.hidden || mutationsList.length < 3) {
                        return;
                    }

                    // 使用更高效的变化检测方法
                    // 1. 首先快速检查是否有添加节点的变化
                    const hasAddedNodes = mutationsList.some(mutation =>
                        mutation.type === 'childList' && mutation.addedNodes.length > 0
                    );

                    if (!hasAddedNodes) {
                        return; // 如果没有添加节点，直接返回
                    }

                    // 2. 使用一个标志来跟踪是否找到了重要变化
                    let hasSignificantChanges = false;

                    // 3. 只检查前几个变化，使用for循环提高效率
                    const maxMutationsToCheck = Math.min(5, mutationsList.length);

                    // 使用更高效的循环和提前退出策略
                    mutationLoop: for (let i = 0; i < maxMutationsToCheck; i++) {
                        const mutation = mutationsList[i];

                        // 只关注添加新节点的变化
                        if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                            const addedNodes = mutation.addedNodes;
                            const nodeCount = Math.min(2, addedNodes.length);

                            // 使用缓存的DOM方法引用提高性能
                            for (let j = 0; j < nodeCount; j++) {
                                const node = addedNodes[j];

                                // 只处理元素节点
                                if (node.nodeType !== Node.ELEMENT_NODE) continue;

                                // 快速检查是否是帖子元素
                                if (isPostElement(node)) {
                                    hasSignificantChanges = true;
                                    break mutationLoop; // 使用标签跳出外层循环
                                }

                                // 如果节点有多个子元素，检查是否包含帖子
                                if (node.children && node.children.length > 2) {
                                    // 使用更具体的选择器减少查询范围
                                    if (node.querySelector('li.post-list-item, .post-title, .topic-title')) {
                                        hasSignificantChanges = true;
                                        break mutationLoop;
                                    }
                                }
                            }
                        }
                    }

                    // 只有在检测到重要变化时才触发过滤
                    if (hasSignificantChanges) {
                        throttledFilter();
                    }
                } catch (error) {
                    // 出错时不做任何处理，避免控制台输出
                }
            });

            // 只观察主要内容区域，而不是整个文档
            // 使用缓存的主要内容容器
            let targetNode = domCache.mainContentContainer || document.body;

            // 如果没有缓存的容器，尝试刷新缓存
            if (!domCache.mainContentContainer) {
                refreshDomCache();
                targetNode = domCache.mainContentContainer || document.body;
            }

            // 使用更精确的配置
            observer.observe(targetNode, {
                childList: true,
                subtree: true,
                attributes: false, // 不监听属性变化，减少触发次数
                characterData: false // 不监听文本变化
            });

            // 保存观察器引用，以便在需要时可以断开连接
            window._nodeseekBlockerObserver = observer;

            // 添加页面可见性变化处理
            document.addEventListener("visibilitychange", () => {
                if (document.hidden) {
                    // 页面不可见时，断开观察器连接
                    if (window._nodeseekBlockerObserver) {
                        window._nodeseekBlockerObserver.disconnect();
                    }
                    // 清除任何待处理的定时器
                    if (throttleTimer) {
                        clearTimeout(throttleTimer);
                        throttleTimer = null;
                    }
                } else {
                    // 页面再次可见时，重新连接观察器
                    setupMutationObserver();
                    // 执行一次过滤，确保内容是最新的
                    setTimeout(() => {
                        if (!document.hidden) {
                            debouncedFilterContent();
                        }
                    }, 1000);
                }
            });
        }

        // 简化版的帖子元素检查函数，仅用于内部使用
        function isPostElement(element) {
            // 只检查最常见的选择器，减少计算量
            if (element.matches && (
                element.matches('li.post-list-item') ||
                element.matches('.post-list-item') ||
                element.matches('.topic-item') ||
                element.matches('.post-item')
            )) {
                return true;
            }

            // 简化的标题检查
            if (element.querySelector && (
                element.querySelector('div.post-title > a') ||
                element.querySelector('.post-title') ||
                element.querySelector('.topic-title')
            )) {
                return true;
            }

            return false;
        }

        // 分析页面结构，帮助调试
        function analyzePageStructure() {
            log("分析页面结构");

            // 尝试找到可能的帖子容器
            const possibleContainers = [
                // Nodeseek 特定选择器
                'li.post-list-item',
                '.post-list-item',
                '.post-list > li',
                // 其他可能的选择器
                'div.media.topic-list-item',
                '.topic-item',
                '.post-item',
                '.thread-item',
                '.node-topic-item',
                '.topic-list > div',
                '.post-list > div',
                'div[data-tid]',
                'article.post',
                '.card.post',
                '.list-group-item'
            ];

            possibleContainers.forEach(selector => {
                const elements = document.querySelectorAll(selector);
                if (elements.length > 0) {
                    log(`找到可能的帖子容器: ${selector}, 数量: ${elements.length}`);

                    // 检查第一个元素的标题
                    const firstElement = elements[0];
                    const possibleTitleElements = firstElement.querySelectorAll('a, h1, h2, h3, h4, .title, [class*="title"]');

                    if (possibleTitleElements.length > 0) {
                        log(`可能的标题元素:`, Array.from(possibleTitleElements).map(el => ({
                            tag: el.tagName,
                            class: el.className,
                            text: el.textContent.trim().substring(0, 50) + (el.textContent.length > 50 ? '...' : '')
                        })));
                    }
                }
            });

            // 尝试找到所有可能的标题元素
            const allPossibleTitles = document.querySelectorAll('a.post-title, .post-title, .topic-title, .thread-title, h2 a, h3 a, .title a, a.title');
            log(`找到可能的标题元素总数: ${allPossibleTitles.length}`);
        }

        // 在帖子标题旁边添加屏蔽按钮
        function addBlockPostButtonsToTitles(parentElement = document) {
            log("尝试在帖子标题旁边添加屏蔽按钮");
            const titleSelectors = [
                // Nodeseek 特定选择器
                'div[role="heading"][aria-level="3"].post-title > a', // 最精确的选择器
                'div.post-title > a',
                'div.post-title a',
                // 其他可能的选择器
                'a.post-title',
                '.post-title a',
                '.topic-title a',
                '.thread-title a',
                'h2 a',
                'h3 a',
                '.title a',
                'a.title',
                '.subject a',
                'a.subject',
                '.card-title a',
                'a.card-title',
                'h4 a',
                '.header a',
                '.heading a'
            ];

            titleSelectors.forEach(selector => {
                const titleElements = parentElement.querySelectorAll(selector);
                titleElements.forEach(titleElement => {
                    // 确保标题元素有效
                    const titleText = titleElement.textContent.trim();
                    if (!titleText || titleText.length === 0) return;

                    // 获取帖子ID
                    let postId = null;
                    const href = titleElement.getAttribute('href');
                    if (href) {
                        // 尝试从URL中提取帖子ID
                        const postIdMatch = href.match(/\/post-(\d+)/) || href.match(/\/t\/(\d+)/) || href.match(/\/thread-(\d+)/);
                        if (postIdMatch && postIdMatch[1]) {
                            postId = postIdMatch[1];
                        } else {
                            // 如果无法从URL中提取，使用URL本身作为ID
                            postId = href;
                        }
                    } else {
                        // 如果没有href属性，跳过
                        return;
                    }

                    // 检查是否已经被屏蔽
                    const isBlocked = blockedPosts.some(post => post.id === postId);

                    // 获取或创建包裹容器
                    let container = titleElement.closest('.post-title-container');
                    if (!container) {
                        // 如果没有找到.post-title-container
                        // 尝试包裹titleElement
                        container = document.createElement('span');
                        container.classList.add('post-title-container');
                        titleElement.parentNode.insertBefore(container, titleElement);
                        container.appendChild(titleElement);
                    }

                    // 确保只添加一次按钮到容器
                    if (container.dataset.blockButtonAdded === 'true') {
                        return;
                    }
                    container.dataset.blockButtonAdded = 'true';

                    // 已屏蔽帖子的处理
                    if (isBlocked) {
                        if (!container.querySelector('.blocked-post-indicator')) {
                            const indicator = document.createElement('span');
                            indicator.textContent = ' (已屏蔽)';
                            indicator.className = 'blocked-post-indicator';
                            indicator.style.color = 'grey';
                            indicator.style.fontSize = '0.9em';
                            indicator.style.marginLeft = '4px';
                            // 插入到标题元素之后
                            if (titleElement.nextSibling) {
                                titleElement.parentNode.insertBefore(indicator, titleElement.nextSibling);
                            } else {
                                titleElement.parentNode.appendChild(indicator);
                            }
                        }
                        return; // 不为已屏蔽帖子添加屏蔽按钮
                    }

                    const blockButton = document.createElement('span');
                    blockButton.textContent = '屏蔽'; // 更简洁的按钮文字
                    blockButton.className = 'block-post-hover-button'; // 使用新的 CSS 类
                    blockButton.title = `屏蔽帖子: ${titleText}`;

                    blockButton.addEventListener('click', (e) => {
                        e.preventDefault();
                        e.stopPropagation();

                        // 直接执行屏蔽操作
                        if (addBlockedPost(postId, titleText)) {
                            // 移除按钮，只留下永久的"(已屏蔽)"指示符
                            blockButton.remove();

                            // 添加已屏蔽标记 (如果尚不存在)
                            if (!container.querySelector('.blocked-post-indicator')) {
                                const indicator = document.createElement('span');
                                indicator.textContent = ' (已屏蔽)';
                                indicator.className = 'blocked-post-indicator';
                                indicator.style.color = 'grey';
                                indicator.style.fontSize = '0.9em';
                                indicator.style.marginLeft = '4px';
                                // 确保指示符直接附加到标题元素之后
                                if (titleElement.nextSibling) {
                                    container.insertBefore(indicator, titleElement.nextSibling);
                                } else {
                                    container.appendChild(indicator);
                                }
                            }

                            // 隐藏帖子
                            const postItem = titleElement.closest('li.post-list-item') ||
                                            titleElement.closest('.post-list-item') ||
                                            titleElement.closest('.topic-item') ||
                                            titleElement.closest('.post-item');
                            if (postItem) {
                                postItem.style.display = 'none';
                                postItem.setAttribute('data-blocked', 'true');
                                postItem.setAttribute('data-blocked-reason', 'post');
                                postItem.setAttribute('data-blocked-value', postId);
                            }
                        }
                    });

                    // 将按钮添加到容器中
                    container.appendChild(blockButton);
                });
            });
        }

        // 新增：在用户名旁边添加屏蔽按钮
        function addBlockUserButtonsToUsernames(parentElement = document) {
            log("尝试在用户名旁边添加屏蔽按钮");
            // 缓存常用选择器结果
            const usernameElements = parentElement.querySelectorAll('.info-item.info-author a, .author-name, a[href^="/space/"]:not(.info-item.info-last-commenter a)');

            // 其他选择器按需延迟查询
            if (usernameElements.length === 0) {
                const secondaryElements = parentElement.querySelectorAll('div.post-meta a[href^="/user/"], .meta-item a.author-link, .name > a[href*="/user/"], .author > a[href*="/user/"], .comment-author a[href*="/user/"], a.username[href*="/user/"]');
                processUsernameElements(secondaryElements);
            } else {
                processUsernameElements(usernameElements);
            }

            function processUsernameElements(elements) {
                elements.forEach(userElement => {
                    // 确保用户名元素有效
                    const username = userElement.textContent.trim();
                    if (!username || username.length === 0 || username.length >= 50) return;

                    // 获取或创建包裹容器
                    let container = userElement.closest('.author-name-container');

                    // 检查是否是主页列表中的用户名
                    const isInfoAuthor = userElement.closest('.info-item.info-author');
                    if (isInfoAuthor) {
                        // 如果是主页列表中的用户名，使用.info-item.info-author作为容器
                        container = isInfoAuthor;
                    } else if (!container) {
                        // 如果不是主页列表中的用户名，且没有找到.author-name-container
                        // 尝试包裹userElement的直接父节点，如果它不是block或者没有其他重要子节点
                        const parent = userElement.parentElement;
                        if (parent && (getComputedStyle(parent).display === 'inline' || parent.childNodes.length === 1)) {
                             container = parent;
                             if (!container.classList.contains('author-name-container')) {
                                container.classList.add('author-name-container');
                             }
                        } else {
                            // 如果父元素不适合做容器，或者userElement是更复杂的结构的一部分，
                            // 则在userElement旁边创建一个span作为容器。
                            container = document.createElement('span');
                            container.classList.add('author-name-container');
                            userElement.parentNode.insertBefore(container, userElement);
                            container.appendChild(userElement);
                        }
                    }

                    // 确保只添加一次按钮到容器
                    if (container.dataset.blockButtonAdded === 'true') {
                        return;
                    }
                    container.dataset.blockButtonAdded = 'true';

                    // 已屏蔽用户的处理
                    if (blockedUsernames.has(username)) {
                        if (!container.querySelector('.blocked-user-indicator')) {
                            const indicator = document.createElement('span');
                            indicator.textContent = ' (已屏蔽)';
                            indicator.className = 'blocked-user-indicator';
                            indicator.style.color = 'grey';
                            indicator.style.fontSize = '0.9em';
                            indicator.style.marginLeft = '4px';
                             // 插入到用户名元素之后，但在屏蔽按钮之前（如果按钮已存在或将被创建）
                            if (userElement.nextSibling) {
                                userElement.parentNode.insertBefore(indicator, userElement.nextSibling);
                            } else {
                                userElement.parentNode.appendChild(indicator);
                            }
                        }
                        return; // 不为已屏蔽用户添加屏蔽按钮
                    }

                    const blockButton = document.createElement('span');
                    blockButton.textContent = '屏蔽'; // 更简洁的按钮文字
                    blockButton.className = 'block-user-hover-button'; // 使用新的 CSS 类
                    blockButton.title = `屏蔽用户: ${username}`;

                    blockButton.addEventListener('click', (e) => {
                        e.preventDefault();
                        e.stopPropagation();

                        // 直接执行屏蔽操作，不再弹出确认框
                        if (!blockedUsernames.has(username)) {
                            // 使用Set的特性，直接添加（Set会自动处理重复项）
                            blockedUsernames.add(username);

                            // 转换回数组以便存储，确保新添加的用户名在前面
                            const usernamesArray = [username, ...Array.from(blockedUsernames).filter(u => u !== username)];

                            // 确保列表长度不超过50个
                            const limitedArray = usernamesArray.slice(0, 50);
                            blockedUsernames = new Set(limitedArray); // 更新Set

                            if (safeSetValue("nodeseek_blockedUsernames", limitedArray)) {
                                showNotification(`已屏蔽用户: ${username}`);
                                updateUsernameListDisplay();
                                debouncedFilterContent();

                                // 移除按钮，只留下永久的“(已屏蔽)”指示符
                                blockButton.remove();

                                // 添加已屏蔽标记 (如果尚不存在)
                                if (!container.querySelector('.blocked-user-indicator')) {
                                    const indicator = document.createElement('span');
                                    indicator.textContent = ' (已屏蔽)';
                                    indicator.className = 'blocked-user-indicator';
                                    indicator.style.color = 'grey';
                                    indicator.style.fontSize = '0.9em';
                                    indicator.style.marginLeft = '4px';
                                    // 确保指示符直接附加到用户名元素之后，如果用户名元素存在的话
                                    if (userElement && userElement.parentNode === container) { // 常见情况，userElement是container的直接子元素
                                        if (userElement.nextSibling) {
                                            container.insertBefore(indicator, userElement.nextSibling);
                                        } else {
                                            container.appendChild(indicator);
                                        }
                                    } else { // Fallback: append to container if userElement context is unusual
                                        container.appendChild(indicator);
                                    }
                                }

                                // 尝试隐藏整个帖子项
                                // 首先检查是否在帖子内页
                                const isPostDetailPage = userElement.closest('.nsk-content-meta-info');
                                if (isPostDetailPage) {
                                    // 在帖子内页，需要隐藏整个帖子内容
                                    log(`在帖子内页找到需要屏蔽的用户 "${username}"`);

                                    // 首先尝试找到 content-item 元素（评论项）
                                    const contentItem = isPostDetailPage.closest('li.content-item');
                                    if (contentItem) {
                                        // 隐藏整个评论项
                                        log(`隐藏评论项 content-item`);
                                        contentItem.style.display = 'none';
                                        contentItem.setAttribute('data-blocked', 'true');
                                        contentItem.setAttribute('data-blocked-reason', 'username');
                                        contentItem.setAttribute('data-blocked-value', username);
                                        return; // 已处理评论项，不需要继续查找
                                    }

                                    // 如果找不到 content-item，尝试查找其他帖子容器
                                    const postContainer = isPostDetailPage.closest('article') ||
                                                         isPostDetailPage.closest('.post-detail') ||
                                                         isPostDetailPage.closest('.post-content');

                                    if (postContainer) {
                                        // 隐藏整个帖子容器
                                        postContainer.style.display = 'none';
                                        postContainer.setAttribute('data-blocked', 'true');
                                        postContainer.setAttribute('data-blocked-reason', 'username');
                                        postContainer.setAttribute('data-blocked-value', username);
                                    } else {
                                        // 如果找不到帖子容器，至少隐藏 nsk-content-meta-info
                                        isPostDetailPage.style.display = 'none';
                                        isPostDetailPage.setAttribute('data-blocked', 'true');
                                        isPostDetailPage.setAttribute('data-blocked-reason', 'username');
                                        isPostDetailPage.setAttribute('data-blocked-value', username);
                                    }

                                    return; // 已处理帖子内页，不需要继续查找
                                }

                                // 处理主页或其他页面的帖子
                                let postItem = userElement.closest('li.post-list-item') ||
                                               userElement.closest('.post-list-item') ||
                                               userElement.closest('.topic-item') ||
                                               userElement.closest('.post-item') ||
                                               userElement.closest('article');

                                // 如果找不到直接容器，尝试向上查找
                                if (!postItem) {
                                    // 向上查找最近的可能帖子容器
                                    let ancestor = userElement.parentElement;
                                    while (ancestor && !ancestor.matches('li, .post-list-item, .topic-item, .post-item, article')) {
                                        ancestor = ancestor.parentElement;
                                    }
                                    if (ancestor) {
                                        postItem = ancestor;
                                    }
                                }

                                // 如果找到了帖子容器，隐藏它
                                if (postItem) {
                                    // 直接隐藏找到的元素
                                    postItem.style.display = 'none';
                                    postItem.setAttribute('data-blocked', 'true');
                                    postItem.setAttribute('data-blocked-reason', 'username');
                                    postItem.setAttribute('data-blocked-value', username);
                                }
                            }
                        } else {
                            showNotification(`用户 "${username}" 已在屏蔽列表中。`);
                            // 如果用户已在列表中，也确保按钮被移除，只留指示符
                            blockButton.remove();
                            if (!container.querySelector('.blocked-user-indicator')) {
                                const indicator = document.createElement('span');
                                indicator.textContent = ' (已屏蔽)';
                                indicator.className = 'blocked-user-indicator';
                                indicator.style.color = 'grey';
                                indicator.style.fontSize = '0.9em';
                                indicator.style.marginLeft = '4px';
                                if (userElement && userElement.parentNode === container) {
                                    if (userElement.nextSibling) {
                                        container.insertBefore(indicator, userElement.nextSibling);
                                    } else {
                                        container.appendChild(indicator);
                                    }
                                } else {
                                    container.appendChild(indicator);
                                }

                                // 尝试隐藏整个帖子项
                                // 首先检查是否在帖子内页
                                const isPostDetailPage = userElement.closest('.nsk-content-meta-info');
                                if (isPostDetailPage) {
                                    // 在帖子内页，需要隐藏整个帖子内容
                                    log(`在帖子内页找到需要屏蔽的用户 "${username}"`);

                                    // 首先尝试找到 content-item 元素（评论项）
                                    const contentItem = isPostDetailPage.closest('li.content-item');
                                    if (contentItem) {
                                        // 隐藏整个评论项
                                        log(`隐藏评论项 content-item`);
                                        contentItem.style.display = 'none';
                                        contentItem.setAttribute('data-blocked', 'true');
                                        contentItem.setAttribute('data-blocked-reason', 'username');
                                        contentItem.setAttribute('data-blocked-value', username);
                                        return; // 已处理评论项，不需要继续查找
                                    }
                                }

                                // 处理主页或其他页面的帖子
                                let postItem = userElement.closest('li.post-list-item') ||
                                               userElement.closest('.post-list-item') ||
                                               userElement.closest('.topic-item') ||
                                               userElement.closest('.post-item') ||
                                               userElement.closest('article');

                                // 如果找不到直接容器，尝试向上查找
                                if (!postItem) {
                                    // 向上查找最近的可能帖子容器
                                    let ancestor = userElement.parentElement;
                                    while (ancestor && !ancestor.matches('li, .post-list-item, .topic-item, .post-item, article')) {
                                        ancestor = ancestor.parentElement;
                                    }
                                    if (ancestor) {
                                        postItem = ancestor;
                                    }
                                }

                                // 如果找到了帖子容器，隐藏它
                                if (postItem) {
                                    // 无论初始目标是什么，都尝试找到最外层的 li.post-list-item
                                    let itemToActuallyBlock = postItem; // 默认是找到的 postItem

                                    // 先标记当前元素
                                    postItem.setAttribute('data-blocked', 'true');
                                    postItem.setAttribute('data-blocked-reason', 'username');
                                    postItem.setAttribute('data-blocked-value', username);

                                    // 尝试找到最外层的 li.post-list-item
                                    const listItem = postItem.closest('li.post-list-item');
                                    if (listItem) {
                                        itemToActuallyBlock = listItem;
                                    } else if (postItem.classList.contains('post-list-content')) {
                                        // 如果是 post-list-content 但找不到 li.post-list-item，尝试查找父元素
                                        const parentElement = postItem.parentElement;
                                        if (parentElement && (parentElement.tagName.toLowerCase() === 'li' || parentElement.classList.contains('post-list-item'))) {
                                            itemToActuallyBlock = parentElement;
                                        } else {
                                            // 向上查找最多5层，尝试找到 li.post-list-item
                                            let currentElement = postItem.parentElement;
                                            let depth = 0;
                                            while (currentElement && depth < 5) {
                                                if (currentElement.tagName.toLowerCase() === 'li' &&
                                                    (currentElement.classList.contains('post-list-item') ||
                                                     currentElement.parentElement && currentElement.parentElement.classList.contains('post-list'))) {
                                                    itemToActuallyBlock = currentElement;
                                                    break;
                                                }
                                                currentElement = currentElement.parentElement;
                                                depth++;
                                            }
                                        }
                                    }

                                    // 隐藏找到的最合适元素
                                    itemToActuallyBlock.style.display = 'none';
                                    itemToActuallyBlock.setAttribute('data-blocked', 'true');
                                    itemToActuallyBlock.setAttribute('data-blocked-reason', 'username');
                                    itemToActuallyBlock.setAttribute('data-blocked-value', username);
                                }
                            }
                        }
                    });
                    // 将按钮添加到容器中，而不是userElement之后，以利用CSS hover效果
                    container.appendChild(blockButton);
                });
            }
        }

        // 确保脚本只执行一次
        if (window._nodeseekBlockerInitialized) {
            log("脚本已经初始化，跳过重复执行");
            return;
        }
        window._nodeseekBlockerInitialized = true;

        // DOM Ready handling
        if (document.readyState === "loading") {
            document.addEventListener("DOMContentLoaded", () => {
                try {
                    log("DOMContentLoaded 事件触发");
                    init();
                    addBlockUserButtonsToUsernames();
                    addBlockPostButtonsToTitles();
                } catch (error) {
                    handleError(error, "DOMContentLoaded 处理");
                }
            });
        } else {
            // DOMContentLoaded has already fired
            try {
                log("文档已加载，直接初始化");
                init();
                addBlockUserButtonsToUsernames();
                addBlockPostButtonsToTitles();
            } catch (error) {
                handleError(error, "直接初始化");
            }
        }

        // Fallback for sites that might do weird things with DOMContentLoaded
        window.addEventListener("load", () => {
            try {
                log("window.load 事件触发");
                // Ensure init runs if it hasn't for some reason, or re-run key parts if necessary
                if (!document.querySelector(".trigger-area")) { // Check if UI is already up
                    log("UI 未创建，重新初始化");
                    init();
                } else {
                    addBlockUserButtonsToUsernames();
                    addBlockPostButtonsToTitles();
                }
                // Always run a filter pass on full load as well, as some things might appear late
                log("页面完全加载，重新过滤内容");
                debouncedFilterContent();
            } catch (error) {
                handleError(error, "window.load 处理");
            }
        });

        // 额外的延迟检查，确保在AJAX加载的内容上也能工作
        setTimeout(() => {
            try {
                log("延迟检查 (3秒)");
                if (document.querySelector(".trigger-area")) {
                    log("UI 已创建，重新过滤内容");
                    debouncedFilterContent();
                    addBlockUserButtonsToUsernames();
                    addBlockPostButtonsToTitles();
                    analyzePageStructure();
                } else {
                    log("UI 未创建，重新初始化");
                    init();
                }

                // 完全移除周期性检查，改为基于用户交互触发
                // 这样可以大幅减少后台活动，避免触发风控机制

                // 页面可见性变化处理函数
                function handleVisibilityChange() {
                    if (document.hidden) {
                        // 页面不可见时，断开所有连接和定时器
                        if (window._nodeseekBlockerObserver) {
                            window._nodeseekBlockerObserver.disconnect();
                            window._nodeseekBlockerObserver = null;
                        }

                        // 清除所有可能的定时器
                        if (window._nodeseekTimers) {
                            window._nodeseekTimers.forEach(timer => {
                                if (timer) clearTimeout(timer);
                            });
                            window._nodeseekTimers = [];
                        }
                    } else {
                        // 页面可见时，执行一次过滤，然后设置MutationObserver
                        // 使用延迟，确保页面已完全可见
                        const visibilityTimer = setTimeout(() => {
                            if (!document.hidden) {
                                // 只在真正需要时执行过滤
                                if (blockedUsernames.size > 0 || blockedKeywords.size > 0) {
                                    debouncedFilterContent();
                                }

                                // 重新设置MutationObserver
                                setupMutationObserver();
                            }
                        }, 2000);

                        // 记录定时器，以便在页面再次隐藏时清除
                        if (!window._nodeseekTimers) {
                            window._nodeseekTimers = [];
                        }
                        window._nodeseekTimers.push(visibilityTimer);
                    }
                }

                // 监听页面可见性变化
                document.addEventListener("visibilitychange", handleVisibilityChange);

                // 初始设置 - 只在页面可见且有屏蔽内容时执行一次过滤
                if (!document.hidden && (blockedUsernames.size > 0 || blockedKeywords.size > 0)) {
                    // 使用延迟，减少初始加载时的资源消耗
                    const initialTimer = setTimeout(() => {
                        if (!document.hidden) {
                            debouncedFilterContent();
                        }
                    }, 2000);

                    // 记录定时器
                    if (!window._nodeseekTimers) {
                        window._nodeseekTimers = [];
                    }
                    window._nodeseekTimers.push(initialTimer);
                }
            } catch (error) {
                handleError(error, "延迟检查");
            }
        }, 3000);

        // 极度优化的滚动事件处理，减少触发频率
        let scrollTimeout;
        let lastScrollProcessTime = 0;
        let scrollProcessPending = false;

        // 滚动事件处理函数
        function handleScroll() {
            // 如果页面不可见，不处理滚动
            if (document.hidden) {
                return;
            }

            // 如果已经有待处理的滚动，不再添加新的
            if (scrollProcessPending) {
                return;
            }

            // 检查距离上次处理的时间
            const now = Date.now();
            if (now - lastScrollProcessTime < 5000) { // 至少5秒才处理一次滚动
                return;
            }

            try {
                clearTimeout(scrollTimeout);
                scrollProcessPending = true;

                // 延迟处理滚动事件，等待用户停止滚动
                scrollTimeout = setTimeout(() => {
                    // 再次检查页面是否可见
                    if (document.hidden) {
                        scrollProcessPending = false;
                        return;
                    }

                    // 检查是否在主页，如果是，进行特殊处理
                    if (window.location.pathname === '/' || window.location.pathname === '') {
                        // 在主页上，确保能正确识别和屏蔽用户帖子
                        const allPostItems = document.querySelectorAll('li.post-list-item:not([data-blocked-checked])');

                        // 如果有屏蔽的用户名，检查每个帖子
                        if (blockedUsernames.size > 0 && allPostItems.length > 0) {
                            // 只处理前10个未检查的帖子，避免一次处理太多
                            const itemsToProcess = Array.from(allPostItems).slice(0, 10);

                            itemsToProcess.forEach(postItem => {
                                // 标记为已检查，避免重复处理
                                postItem.setAttribute('data-blocked-checked', 'true');

                                // 只检查帖子作者，忽略最后评论者
                                // 帖子作者通常在 .info-item.info-author 中，而最后评论者在 .info-item.info-last-commenter 中
                                const authorLinks = postItem.querySelectorAll('.info-item.info-author a');

                                // 如果找不到特定的作者链接，则使用更通用的选择器，但排除最后评论者
                                if (authorLinks.length === 0) {
                                    const allUserLinks = postItem.querySelectorAll('a[href^="/space/"]');
                                    allUserLinks.forEach(userLink => {
                                        // 确保不是最后评论者
                                        if (!userLink.closest('.info-item.info-last-commenter')) {
                                            const username = userLink.textContent.trim();
                                            if (blockedUsernames.has(username)) {
                                                // 在主页上，确保只隐藏整个帖子项，而不是内部的内容
                                                // 始终自动隐藏被屏蔽的帖子

                                                // 直接隐藏整个 li.post-list-item 元素
                                                postItem.style.display = 'none';
                                                postItem.setAttribute('data-blocked', 'true');
                                                postItem.setAttribute('data-blocked-reason', 'username');
                                                postItem.setAttribute('data-blocked-value', username);
                                            }
                                        }
                                    });
                                } else {
                                    // 使用特定的作者链接
                                    authorLinks.forEach(authorLink => {
                                        const username = authorLink.textContent.trim();
                                        if (blockedUsernames.has(username)) {
                                            // 在主页上，确保只隐藏整个帖子项，而不是内部的内容
                                            // 始终自动隐藏被屏蔽的帖子

                                            // 直接隐藏整个 li.post-list-item 元素
                                            postItem.style.display = 'none';
                                            postItem.setAttribute('data-blocked', 'true');
                                            postItem.setAttribute('data-blocked-reason', 'username');
                                            postItem.setAttribute('data-blocked-value', username);
                                        }
                                    });
                                }
                            });

                            // 如果还有未处理的帖子，安排下一次处理
                            if (allPostItems.length > 10) {
                                setTimeout(() => {
                                    if (!document.hidden) {
                                        debouncedFilterContent();
                                    }
                                }, 1000);
                            }
                        }
                    }

                    // 更新最后处理时间
                    lastScrollProcessTime = Date.now();
                    scrollProcessPending = false;
                }, 1000); // 延迟1秒，等待用户停止滚动
            } catch (error) {
                // 出错时重置状态
                scrollProcessPending = false;
            }
        }

        // 添加滚动事件监听器，使用更高效的节流和事件委托
        let scrollThrottleTimer;
        let lastScrollY = window.scrollY;
        let scrollDistance = 0;

        // 使用事件委托和被动事件监听器提高性能
        window.addEventListener('scroll', () => {
            // 如果页面不可见，不处理
            if (document.hidden) {
                return;
            }

            // 计算滚动距离，只有滚动超过一定距离才处理
            const currentScrollY = window.scrollY;
            scrollDistance += Math.abs(currentScrollY - lastScrollY);
            lastScrollY = currentScrollY;

            // 如果滚动距离不够大，不处理
            if (scrollDistance < 200) {
                return;
            }

            // 使用节流，每800ms最多触发一次，减少处理频率
            if (!scrollThrottleTimer) {
                scrollThrottleTimer = setTimeout(() => {
                    handleScroll();
                    scrollThrottleTimer = null;
                    scrollDistance = 0; // 重置滚动距离
                }, 800);
            }
        }, { passive: true, capture: false }); // 使用被动事件监听器提高滚动性能

        log("脚本设置完成");
    } catch (error) {
        handleError(error, "主脚本");
    }
})();
